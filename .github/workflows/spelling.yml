name: Spelling

on:
  workflow_dispatch:
  push:
    branches: ['dev', 'v1.10', 'v1.11']
  pull_request:
    branches: ['dev', 'v1.10', 'v1.11']

jobs:
  spelling:
    name: Spell Check with Typos
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions Repository
        uses: actions/checkout@v4
      - name: Spell Check Repo
        uses: crate-ci/typos@master
        with:
          config: typos.toml
