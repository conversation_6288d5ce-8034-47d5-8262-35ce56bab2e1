name: amd64-lean-image-build

concurrency:
  group: amd64-lean-image-build-${{ github.head_ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  push:
    branches: ['dev', 'v1.10']
    paths:
      - 'build_sdcard.sh'
      - 'home.admin/bitcoin.install.sh'
      - 'home.admin/tor.install.sh'
      - 'home.admin/blitz.i2pd.sh'
      - 'home.admin/blitz.web.sh'
      - 'home.admin/blitz.display.sh'
      - 'ci/amd64/**'
  pull_request:
    branches: ['dev', 'v1.10']
    paths:
      - 'build_sdcard.sh'
      - 'home.admin/bitcoin.install.sh'
      - 'home.admin/tor.install.sh'
      - 'home.admin/blitz.i2pd.sh'
      - 'home.admin/blitz.web.sh'
      - 'home.admin/blitz.display.sh'
      - 'ci/amd64/**'

jobs:
  amd64-image-build:
    runs-on: ubuntu-22.04
    steps:
      - name: Maximize build space
        uses: easimon/maximize-build-space@master
        with:
          root-reserve-mb: 12288
          temp-reserve-mb: 12288

      - uses: actions/checkout@v4

      - name: Set values
        id: set_values
        run: |
          echo "BUILD_DATE=$(date +"%Y-%m-%d")" >> $GITHUB_ENV
          echo "BUILD_VERSION=$(git describe --always --tags)" >> $GITHUB_ENV
          if [ -z "$GITHUB_HEAD_REF" ]; then
            echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV
          else
            echo "BRANCH_NAME=${GITHUB_HEAD_REF}" >> $GITHUB_ENV
          fi
          if [[ "${{github.event_name}}" == "pull_request" ]]; then
            echo "GITHUB_USER=${{github.event.pull_request.head.repo.owner.login}}" >> $GITHUB_OUTPUT
          else
            echo "GITHUB_USER=$(echo ${{github.repository}} | cut -d'/' -f1)" >> $GITHUB_OUTPUT
          fi

      - name: Display the build name
        run: echo "Building the raspiblitz-amd64-debian-image-${{env.BUILD_DATE}}-${{env.BUILD_VERSION}}"

      - name: Run the build script
        run: |
          echo "Using the variables: --pack lean --github_user ${{steps.set_values.outputs.GITHUB_USER}} --branch ${{env.BRANCH_NAME}} --preseed_file preseed.cfg --boot uefi --desktop gnome --image_type raw"
          cd ci/amd64
          bash packer.build.amd64-debian.sh	--pack lean --github_user ${{steps.set_values.outputs.GITHUB_USER}} --branch ${{env.BRANCH_NAME}} --preseed_file preseed.cfg --boot uefi --desktop gnome --image_type raw

      - name: Compute checksum of the raw image
        run: |
          cd ci/amd64/builds/raspiblitz-amd64-debian-lean-qemu
          sha256sum raspiblitz-amd64-debian-lean.img > raspiblitz-amd64-debian-lean.img.sha256

      - name: Compress image
        run: |
          cd ci/amd64/builds/raspiblitz-amd64-debian-lean-qemu
          gzip -v9 raspiblitz-amd64-debian-lean.img

      - name: Compute checksum of the compressed image
        run: |
          cd ci/amd64/builds/raspiblitz-amd64-debian-lean-qemu
          sha256sum raspiblitz-amd64-debian-lean.img.gz > raspiblitz-amd64-debian-lean.img.gz.sha256

      - name: Upload the image and checksums
        uses: actions/upload-artifact@v4
        with:
          name: raspiblitz-amd64-image-${{env.BUILD_DATE}}-${{env.BUILD_VERSION}}
          path: |
            ${{github.workspace}}/ci/amd64/builds/raspiblitz-amd64-debian-lean-qemu/raspiblitz-amd64-debian-lean.img.sha256
            ${{github.workspace}}/ci/amd64/builds/raspiblitz-amd64-debian-lean-qemu/raspiblitz-amd64-debian-lean.img.gz
            ${{github.workspace}}/ci/amd64/builds/raspiblitz-amd64-debian-lean-qemu/raspiblitz-amd64-debian-lean.img.gz.sha256
