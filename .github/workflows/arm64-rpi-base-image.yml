name: arm64-rpi-base-image-build

concurrency:
  group: arm64-rpi-base-image-build-${{ github.head_ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  push:
    branches: ['dev', 'v1.10', 'v1.11']
    paths:
      - 'build_sdcard.sh'
      - 'home.admin/config.scripts/bitcoin.install.sh'
      - 'home.admin/config.scripts/tor.install.sh'
      - 'home.admin/config.scripts/blitz.i2pd.sh'
      - 'home.admin/config.scripts/blitz.web.sh'
      - 'home.admin/config.scripts/blitz.display.sh'
      - 'ci/arm64-rpi/**'
      - 'home.admin/config.scripts/bonus.btc-rpc-explorer.sh'
      - 'home.admin/config.scripts/bonus.btcpayserver.sh'
      - 'home.admin/config.scripts/bonus.jam.sh'
      - 'home.admin/config.scripts/bonus.joinmarket.sh'
      - 'home.admin/config.scripts/bonus.lnbits.sh'
      - 'home.admin/config.scripts/bonus.mempool.sh'
      - 'home.admin/config.scripts/bonus.nodejs.sh'
      - 'home.admin/config.scripts/bonus.rtl.sh'
      - 'home.admin/config.scripts/bonus.thunderhub.sh'
      - 'home.admin/config.scripts/blitz.web.api.sh'
      - 'home.admin/config.scripts/blitz.web.ui'
  pull_request:
    branches: ['dev', 'v1.10', 'v1.11']
    paths:
      - 'build_sdcard.sh'
      - 'home.admin/config.scripts/bitcoin.install.sh'
      - 'home.admin/config.scripts/tor.install.sh'
      - 'home.admin/config.scripts/blitz.i2pd.sh'
      - 'home.admin/config.scripts/blitz.web.sh'
      - 'home.admin/config.scripts/blitz.display.sh'
      - 'ci/arm64-rpi/**'
      - 'home.admin/config.scripts/bonus.btc-rpc-explorer.sh'
      - 'home.admin/config.scripts/bonus.btcpayserver.sh'
      - 'home.admin/config.scripts/bonus.jam.sh'
      - 'home.admin/config.scripts/bonus.joinmarket.sh'
      - 'home.admin/config.scripts/bonus.lnbits.sh'
      - 'home.admin/config.scripts/bonus.mempool.sh'
      - 'home.admin/config.scripts/bonus.nodejs.sh'
      - 'home.admin/config.scripts/bonus.rtl.sh'
      - 'home.admin/config.scripts/bonus.thunderhub.sh'
      - 'home.admin/config.scripts/blitz.web.api.sh'
      - 'home.admin/config.scripts/blitz.web.ui'

jobs:
  arm64-rpi-base-image-build:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4

      - name: Set values
        id: set_values
        run: |
          echo "BUILD_VERSION=$(git describe --always --tags)" >> $GITHUB_ENV
          if [ -z "$GITHUB_HEAD_REF" ]; then
            echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV
          else
            echo "BRANCH_NAME=${GITHUB_HEAD_REF}" >> $GITHUB_ENV
          fi
          if [[ "${{github.event_name}}" == "pull_request" ]]; then
            echo "GITHUB_USER=${{github.event.pull_request.head.repo.owner.login}}" >> $GITHUB_OUTPUT
          else
            echo "GITHUB_USER=$(echo ${{github.repository}} | cut -d'/' -f1)" >> $GITHUB_OUTPUT
          fi

      - name: Display the build name
        run: echo "Building the raspiblitz-arm64-rpi-base-image"

      - name: Run the build script
        run: |
          echo "Using the variables: --pack base --github_user ${{steps.set_values.outputs.GITHUB_USER}} --branch ${{env.BRANCH_NAME}} --image_size 18G"
          cd ci/arm64-rpi
          bash packer.build.arm64-rpi.sh --pack base --github_user ${{steps.set_values.outputs.GITHUB_USER}} --branch ${{env.BRANCH_NAME}} --image_size 18G

      - name: Calculate the checksum of the raw image
        run: |
          cd ci/arm64-rpi
          sha256sum raspiblitz-arm64-rpi-base.img > raspiblitz-arm64-rpi-base.img.sha256

      - name: Upload the base image and checksum
        uses: actions/upload-artifact@v4
        with:
          name: raspiblitz-arm64-rpi-base-image-${{ env.BUILD_VERSION }}
          path: |
            ${{ github.workspace }}/ci/arm64-rpi/raspiblitz-arm64-rpi-base.img.sha256
            ${{ github.workspace }}/ci/arm64-rpi/raspiblitz-arm64-rpi-base.img
