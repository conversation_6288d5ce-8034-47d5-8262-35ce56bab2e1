# ![RaspiBlitz](pictures/raspilogo_tile_400px.png)

_Build your own Bitcoin & Lightning Fullnode on a RaspberryPi with an optional Display._ ([API](https://github.com/fusion44/blitz_api)|[WebUI](https://github.com/raspiblitz/raspiblitz-web))

![RaspiBlitz](pictures/raspiblitz.jpg)

**The RaspiBlitz is a do-it-yourself Bitcoin & Lightning Fullnode running on a RaspberryPi 4&5 with a nice display for easy setup & monitoring.**

RaspiBlitz is mainly targeted for learning how to run your own node decentralized from home - because: Not your Node, Not your Rules. Discover & develop the open-source ecosystem of Bitcoin by becoming a full part of it.

**Links to Quickstart your RaspiBlitz journey:**

- [Project Homepage: raspiblitz.org](https://raspiblitz.org)
- [How to build & setup your own RaspiBlitz & Documentation](https://docs.raspiblitz.org/docs/setup/intro)
- [Download latest SD Card images](https://docs.raspiblitz.org/docs/setup/software-setup/download)
- [How to get Support](https://docs.raspiblitz.org/docs/community/support)

**Additional Resources:**

- [ChangeLog](CHANGES.md)
- [FAQ User](https://docs.raspiblitz.org/docs/faq)
- [FAQ Development](https://docs.raspiblitz.org/docs/faq/dev)
- [FAQ Core Lightning](https://docs.raspiblitz.org/docs/faq/cl)
- [Workshop Tutorial](https://docs.raspiblitz.org/docs/community/workshops)
- [Security Policy](https://docs.raspiblitz.org/docs/security)
- [Alternative Platforms](alternative.platforms/README.md)
- [Automated Builds](ci/README.md)
- [MIT OpenSource License](LICENSE)

**Developer Notes:**

This is main RaspiBlitz repo containing the **bash & python** scripts to build the RaspiBlitz software. It it complimented by the following side repos:

- [WebUI](https://github.com/raspiblitz/raspiblitz-web) (React & Tailwind)
- [API](https://github.com/fusion44/blitz_api) (Python FastAPI)
- [Documentation](https://github.com/raspiblitz/raspiblitz-docs) (Docusaurus)

To get started with RaspiBlitz Development check the [Community Development](CONTRIBUTING.md) notes.
