# https://github.com/chef/bento/blob/main/packer_templates/http/debian/preseed.cfg
# https://www.debian.org/releases/stable/example-preseed.txt
# https://github.com/tylert/packer-build/blob/master/source/debian/12_bookworm/base-uefi.preseed
# variables: https://github.com/tylert/packer-build/blob/master/source/debian/12_bookworm/base-uefi.pkr.hcl

# Locale Setup
d-i debian-installer/language string en
d-i debian-installer/country string US
d-i debian-installer/locale string en_US.UTF-8
# d-i localechooser/supported-locales multiselect en_CA.UTF-8 fr_CA.UTF-8 zh_CN.UTF-8
# d-i pkgsel/install-language-support boolean true

# Keyboard Setup
d-i keyboard-configuration/xkb-keymap select us

# Clock Setup
# d-i time/zone string Canada/Eastern
d-i time/zone string UTC
d-i clock-setup/utc boolean true
# set above to false if making a bootable USB to run on same system as Windows

# Network Setup
d-i netcfg/get_hostname string raspiblitz-amd64
d-i netcfg/get_domain string
# https://bugs.launchpad.net/ubuntu/+source/netcfg/+bug/713385
d-i netcfg/choose_interface select auto
# make sure you also add "interface=auto" to your boot command too
# https://bugs.launchpad.net/ubuntu/+source/netcfg/+bug/713385

# User Setup
d-i passwd/root-login boolean false
d-i passwd/root-password-again password raspiblitz
d-i passwd/root-password password raspiblitz
d-i passwd/user-fullname string pi
d-i passwd/user-uid string 1000
d-i passwd/user-password password raspiblitz
d-i passwd/user-password-again password raspiblitz
d-i passwd/username string pi

# Package Setup
d-i hw-detect/load_firmware boolean false
d-i hw-detect/load_media boolean false
apt-cdrom-setup apt-setup/cdrom/set-first boolean false
d-i mirror/country string manual
d-i mirror/http/hostname string httpredir.debian.org
d-i mirror/http/directory string /debian
d-i mirror/http/proxy string
d-i apt-setup/contrib boolean true
d-i apt-setup/non-free boolean true

tasksel tasksel/first multiselect ssh-server, standard
d-i pkgsel/include string sudo bzip2 acpid cryptsetup zlib1g-dev wget curl dkms fuse make nfs-common net-tools cifs-utils rsync
d-i pkgsel/install-language-support boolean false
d-i pkgsel/update-policy select none
d-i pkgsel/upgrade select full-upgrade

popularity-contest popularity-contest/participate boolean false

# Drive setup
d-i partman-auto-lvm/guided_size string max
d-i partman-auto/choose_recipe select atomic
d-i partman-auto/method string lvm
d-i partman-lvm/confirm boolean true
d-i partman-lvm/confirm_nooverwrite boolean true
d-i partman-lvm/device_remove_lvm boolean true
d-i partman/choose_partition select finish
d-i partman/confirm boolean true
d-i partman/confirm_nooverwrite boolean true
d-i partman/confirm_write_new_label boolean true

# Final Setup
d-i finish-install/reboot_in_progress note
