# IMPORTANT:
# - Modifications to /boot/dietpi.txt will not be preserved on reboot.
# - Please ensure you edit from the DietPi-RAMdisk location: /DietPi/dietpi.txt

# NB: This is intended for advanced users, unless you know what you are doing, do not edit this file. Please use the DietPi programs instead.
# NB: Do not remove uncommented lines, as the items are scraped by DietPi programs, on demand.

#------------------------------------------------------------------------------------------------------
# D I E T - P I
# DietPi-Automation settings, applied on the 1st boot of DietPi, ONCE
#------------------------------------------------------------------------------------------------------

##### Networking Options #####

#    If both Ethernet and Wifi are enabled, Wifi will take priority and Ethernet will be disabled.
#        1=enabled
AUTO_SETUP_NET_ETHERNET_ENABLED=1
AUTO_SETUP_NET_WIFI_ENABLED=0

#    If using WiFi, please edit the following to pre-enter creds /boot/dietpi-wifi.txt

#    Enter your Static Network details below, if applicable.
AUTO_SETUP_NET_USESTATIC=0
AUTO_SETUP_NET_STATIC_IP=***********00
AUTO_SETUP_NET_STATIC_MASK=*************
AUTO_SETUP_NET_STATIC_GATEWAY=***********
AUTO_SETUP_NET_STATIC_DNS=*******

#    Hostname
AUTO_SETUP_NET_HOSTNAME=DietPi

#    Force ethernet speeds
#        NB: This is mainly aimed at Pine A64's which may have an HW issue that causes unstable 1Gbit link.
#        0=automatic speed | 10 = 10mbit, 100 = 100mbit etc
AUTO_SETUP_NET_ETH_FORCE_SPEED=0

##### Misc Options #####

#    Size of swapfile to generate (MB)
#        0=Disabled | 1=auto (2GB-RAM = size) | 2+=manual
AUTO_SETUP_SWAPFILE_SIZE=1
#        Optional swapfile location
AUTO_SETUP_SWAPFILE_LOCATION=/var/swap

# Unmask (enable) systemd-logind service, which is masked by default on DietPi
AUTO_UNMASK_LOGIND=0

##### Software Automation Options #####

#    Fully automate the installation
#        1=Automated installation with no user inputs.
#        It is HIGHLY recommended to also set CONFIG_BOOT_WAIT_FOR_NETWORK=2, to force infinite wait for network connection during boot, preventing no connection errors due to timeout.
AUTO_SETUP_AUTOMATED=0

#    Global Password to be applied for the system
#        Requires AUTO_SETUP_AUTOMATED=1
#        Affects user "root" and "dietpi" login passwords, and, all software installed by dietpi-software, that requires a password
#        eg: MySQL, Transmission, Deluge etc.
#        WARN: Passwords with the any of the following characters are not supported: \"$
#        WARN: Do NOT change this entry after 1st run setup of DietPi has been completed. It is always scraped by dietpi-software.
AUTO_SETUP_GLOBAL_PASSWORD=dietpi

#    DietPi-Software to automatically install. | requires AUTO_SETUP_AUTOMATED=1
#    For a list of software index's (ID's), run '/DietPi/dietpi/dietpi-software list'
#    No limit on number entries, add as many as you need and uncomment the line.
#    DietPi will automatically install all pre-reqs (eg: ALSA/XSERVER for desktops etc)
#    - Examples:
#AUTO_SETUP_INSTALL_SOFTWARE_ID=23    #will install Desktop LXDE
#AUTO_SETUP_INSTALL_SOFTWARE_ID=74    #will install LAMP webserver stack
#AUTO_SETUP_INSTALL_SOFTWARE_ID=44    #will install Bittorrent transmission

#    DietPi-Software Choice System
#    SSH Server Selection:
#        0=none
#        -1=dropbear
#        -2=opensshserver
AUTO_SETUP_SSH_SERVER_INDEX=-1

#    File Server Selection:
#        0=none/manual
#        -1=proftp
#        -2=samba
AUTO_SETUP_FILE_SERVER_INDEX=0

#    Logging Mode Selection:
#        0=none/manual
#        -1=ramlog 1h clear
#        -2=ramlog 1h save clear
#        -3=logrotate + rsyslog
AUTO_SETUP_LOGGING_INDEX=-1
#    RAMlog max tmpfs size (MB). 50MB should be fine for single use. 200MB+ for heavy webserver and access log use etc.
AUTO_SETUP_RAMLOG_MAXSIZE=50

#    Webserver Preference Selection:
#    NB: This will get ignored, if you have manually selected any WEBSERVER_Stack.
#        0=Apache2
#        -1=Nginx
#        -2=Lighttpd
AUTO_SETUP_WEB_SERVER_INDEX=-2

#    DietPi-Autostart | Requires AUTO_SETUP_AUTOMATED=1
#    After installation is completed, which program should the system boot to?
#        0=Console 7=Console+auto root login | 1=Kodi 2=Desktops (LXDE/MATE etc) 5=DietPi-Cloudshell 6=Uae4ARM (Fastboot) 8=Uae4ARM (standard boot) 9=dxx-rebirth
AUTO_SETUP_AUTOSTART_TARGET_INDEX=0

#    Language/Regional settings | Requires AUTO_SETUP_AUTOMATED=1
#        Timezone eg: Europe/London America/New_York | Full list (TZ*): https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
AUTO_SETUP_TIMEZONE=Europe/London
#        Locale eg: en_GB.UTF-8 / en_US.UTF-8 etc. One entry ONLY.
AUTO_SETUP_LOCALE=en_GB.UTF-8
#        Keyboard Layout eg: gb us de fr
AUTO_SETUP_KEYBOARD_LAYOUT=gb

#    Custom Script (pre-networking and pre-DietPi install) | Runs before DietPi installation and networking
#        Allows you to automatically execute a custom script before networking and DietPi installation is started
#        Option 1 = Copy your script to /boot/Automation_Custom_PreScript.sh and it will be executed automatically.
#        NB: Executed script log /var/tmp/dietpi/logs/dietpi-automation_custom_prescript.log

#    Custom Script (post-networking and post-DietPi install) | Runs after DietPi installation is completed
#        Allows you to automatically execute a custom script at the end of DietPi installation.
#        Option 1 = Copy your script to /boot/Automation_Custom_Script.sh and it will be executed automatically.
#        Option 2 = Host your script online, then use AUTO_SETUP_CUSTOM_SCRIPT_EXEC=http://myweb.com/myscript.sh , it will be downloaded and executed automatically. | 0=disabled
#        NB: Executed script log /var/tmp/dietpi/logs/dietpi-automation_custom_script.log
AUTO_SETUP_CUSTOM_SCRIPT_EXEC=0

#Disable HDMI (and GPU/VPU where supported) output for supported devices:
#	RPi | Odroid C1 | Odroid C2
AUTO_SETUP_HEADLESS=0

#------------------------------------------------------------------------------------------------------
# D I E T - P I
# DietPi-Config settings
#------------------------------------------------------------------------------------------------------

#Cpu Governor | ondemand | powersave | performance | conservative
CONFIG_CPU_GOVERNOR=ondemand
CONFIG_CPU_USAGE_THROTTLE_UP=50

#CPU Frequency Limits
#    NB: Intel CPU's use a percentage value (%) from 0-100 (eg: 55)
#    NB: All other devices must use a specific MHz value (eg: 1600)
#    Limit the MAX CPU frequency for all cores | Disabled=disabled
CONFIG_CPU_MAX_FREQ=Disabled
#    Limit the MIN CPU frequency for all cores | Disabled=disabled
CONFIG_CPU_MIN_FREQ=Disabled

#    Disable Intel-based turbo/boost stepping. This flag should not be required, setting <100% MAX frequency should disable Turbo on Intel CPU's.
CONFIG_CPU_DISABLE_TURBO=0

#Min value 10000 microseconds (10ms)
CONFIG_CPU_ONDEMAND_SAMPLE_RATE=25000

#sampling rate * down factor / 1000 = Milliseconds (40 = 1000ms when sampling rate is 25000)
CONFIG_CPU_ONDEMAND_SAMPLE_DOWNFACTOR=80

#Proxy settings | System-wide proxy settings. Use dietpi-config > networking options to apply.
#    NB: Do not modify, you must use dietpi-config to configure/set options
CONFIG_PROXY_ENABLED=0
CONFIG_PROXY_ADDRESS=MyProxyServer.com
CONFIG_PROXY_PORT=8080
CONFIG_PROXY_USERNAME=
CONFIG_PROXY_PASSWORD=

#Delay boot until network is established: 0=disabled | 1=10 second wait max (default) | 2=infinite wait
CONFIG_BOOT_WAIT_FOR_NETWORK=1

#DietPi checks for updates (allows dietpi to check for updates on a daily basis and boot using a <1kb file download.)
CONFIG_CHECK_DIETPI_UPDATES=1
#	Optional: Automatically update DietPi when updates are available. | requires CONFIG_CHECK_DIETPI_UPDATES=1
CONFIG_AUTO_DIETPI_UPDATES=0

#NTPD Update Mode: 0=disabled | 1=boot only | 2=boot + daily | 3=boot + hourly | 4=Daemon + Drift
CONFIG_NTP_MODE=2

#WiFi country code. 2 character value (eg GB US DE JP): https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
CONFIG_WIFI_COUNTRY_CODE=GB

#Serial Console: Set to 0 if you do not require serial console.
CONFIG_SERIAL_CONSOLE_ENABLE=1

#Soundcard
CONFIG_SOUNDCARD=none

#LCD Panel addon
#    NB: Do not modify, you must use dietpi-config to configure/set options
CONFIG_LCDPANEL=none

#IPv6
CONFIG_ENABLE_IPV6=1

#Prefer IPv4 with APT and wget, NB: This has no effect if IPv6 is disabled anyway!
CONFIG_PREFER_IPV4=1

#APT mirrors which are applied to /etc/apt/sources.list | Values here will also be applied during 1st run setup
#    Raspbian = https://www.raspbian.org/RaspbianMirrors
#    Debian = https://www.debian.org/mirror/official#list
CONFIG_APT_RASPBIAN_MIRROR=http://raspbian.raspberrypi.org/raspbian
CONFIG_APT_DEBIAN_MIRROR=https://deb.debian.org/debian/

#NTPD mirror, applied to /etc/ntp.conf
#    For a full list, please see http://www.pool.ntp.org
#    Please remove the initial integer and full stop from the value (removing 0.). eg: debian.pool.ntp.org
CONFIG_NTP_MIRROR=debian.pool.ntp.org

#------------------------------------------------------------------------------------------------------
# D I E T - P I
# DietPi-Software settings
#------------------------------------------------------------------------------------------------------
#Enter your EmonCMS.org write API key here. It will be applied automatically during EmonPi/Hub installation.
#    eg: SOFTWARE_EMONHUB_APIKEY=b4dfmk2o203mmxx93a
SOFTWARE_EMONHUB_APIKEY=

#VNC Server Options
SOFTWARE_VNCSERVER_WIDTH=1280
SOFTWARE_VNCSERVER_HEIGHT=720
SOFTWARE_VNCSERVER_DEPTH=16
SOFTWARE_VNCSERVER_DISPLAY_INDEX=1
SOFTWARE_VNCSERVER_SHARE_DESKTOP=0

#Optional username for ownCloud/Nextcloud admin account, the default is 'admin'. Applied during installation.
SOFTWARE_OWNCLOUD_NEXTCLOUD_USERNAME=admin

#Optional data directory for ownCloud, default is '/mnt/dietpi_userdata/owncloud_data'. Applied during installation.
#    This option is for advanced users. For full compatibility, please keep this options defaults, and, use dietpi-drive_manager to move the DietPi user data location.
SOFTWARE_OWNCLOUD_DATADIR=/mnt/dietpi_userdata/owncloud_data

#Optional data directory for Nextcloud, default is '/mnt/dietpi_userdata/nextcloud_data'. Applied during installation.
#    This option is for advanced users. For full compatibility, please keep this options defaults, and, use dietpi-drive_manager to move the DietPi user data location.
SOFTWARE_NEXTCLOUD_DATADIR=/mnt/dietpi_userdata/nextcloud_data

#Wifi Hotspot
SOFTWARE_WIFI_HOTSPOT_SSID=DietPi-HotSpot
#    minimum of 8 characters
SOFTWARE_WIFI_HOTSPOT_KEY=dietpihotspot
SOFTWARE_WIFI_HOTSPOT_CHANNEL=3

#Xorg options
#	DPI 96(default) 120(+25%) 144(+50%) 168(+75%) 192(+100%)
SOFTWARE_XORG_DPI=96

#Chromium Options
SOFTWARE_CHROMIUM_RES_X=1280
SOFTWARE_CHROMIUM_RES_Y=720
SOFTWARE_CHROMIUM_AUTOSTART_URL=https://google.com

#------------------------------------------------------------------------------------------------------
# D I E T - P I
# Dev settings
#------------------------------------------------------------------------------------------------------
DEV_GITBRANCH=master
DEV_GITOWNER=Fourdee

#------------------------------------------------------------------------------------------------------
# D I E T - P I
# Settings, automatically added by dietpi-update
#------------------------------------------------------------------------------------------------------