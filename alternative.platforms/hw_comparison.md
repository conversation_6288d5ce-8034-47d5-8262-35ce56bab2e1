## SBC benchmarks:

https://github.com/ThomasKaiser/sbc-bench/blob/master/Results.md

https://dietpi.com/survey/#benchmark

###  Raspberry Pi 3 Model B+

* Broadcom BCM2837B0, Cortex-A53 (ARMv8) 64-bit SoC @ 1.4GHz
* 1GB LPDDR2 SDRAM
* 2.4GHz and 5GHz IEEE 802.11.b/g/n/ac wireless LAN, Bluetooth 4.2, BLE
* Gigabit Ethernet over USB 2.0 (maximum throughput 300 Mbps)
* Extended 40-pin GPIO header
* Full-size HDMI
* 4 USB 2.0 ports
* CSI camera port for connecting a Raspberry Pi camera
* DSI display port for connecting a Raspberry Pi touchscreen display
* 4-pole stereo output and composite video port
* Micro SD port for loading your operating system and storing data
* 5V/2.5A DC power input
* Power-over-Ethernet (PoE) support (requires separate PoE HAT)

### Raspberry Pi 4

* SoC: Broadcom BCM2711B0 quad-core A72 (ARMv8-A) 64-bit @ 1.5GHz
* GPU: Broadcom VideoCore VI
* Networking: 2.4 GHz and 5 GHz 802.11b/g/n/ac wireless LAN
* RAM: 1GB, 2GB, or 4GB LPDDR4 SDRAM
* Bluetooth: Bluetooth 5.0, Bluetooth Low Energy (BLE)
* GPIO: 40-pin GPIO header, populated
* Storage: microSD
* Ports: 2 × micro-HDMI 2.0, 3.5 mm analogue audio-video jack, 2 ×* USB 2.0, 2 × USB 3.0, Gigabit Ethernet, Camera Serial Interface* (CSI), Display Serial Interface (DSI)
* Dimensions: 88 mm × 58 mm × 19.5 mm, 46 g

### Odroid HC1

* Samsung Exynos5422 Octa core CPU 4x Cortex-A15 2Ghz and 4x Cortex-A7 1.5GHz
* 2 Gbyte LPDDR3 RAM
* SATA-3 port for 2.5inch HDD/SSD storage up to 15mm thickness
* Gigabit Ethernet port
* USB 2.0 Host
* UHS-1 capable micro-SD card slot for boot media
* Size : 147 x 85 x 29 mm approx.(including Aluminium cooling frame)
* Linux server OS images based on modern Kernel 4.14 LTS

![HC1](/alternative.platforms/pictures/HC1.jpg)

### Odroid XU4

* Samsung Exynos5422 Octa core CPU 4x Cortex-A15 2Ghz and 4x Cortex-A7 1.5GHz
* 2 Gbyte LPDDR3 RAM 
* Graphics: Samsung S2MPS11
* Storage: eMMC5.0 HS400 Flash Storage or SD Card
* I/O Connectors: HDMI-A x 1, USB 3.0 Host x 2, USB 2.0 Host x 1, PWM for * Cooler Fan, UART for serial console 30Pin : GPIO/IRQ/SPI/ADC, 12Pin : GPIO/I2S/I2C
* Network Ethernet RJ-45
* Input Power 5V

![XU4](/alternative.platforms/pictures/XU4.jpg)