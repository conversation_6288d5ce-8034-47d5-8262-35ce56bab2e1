#!/bin/bash

# deprecated - see: https://github.com/rootzoll/raspiblitz/issues/2290

# command info
if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "-help" ]; then
 echo "small config script to change between testnet and mainnet"
 echo "network.chain.sh [testnet|mainnet]"
 exit 1
fi

# check input
if [ "$1" != "testnet" ] && [ "$1" != "mainnet" ]; then
 echo "FAIL - unknown value: $1"
 exit 1
fi

# check and load raspiblitz config
# to know which network is running
source /home/<USER>/raspiblitz.info
source /mnt/hdd/app-data/raspiblitz.conf
if [ ${#network} -eq 0 ]; then
 echo "FAIL - missing network info"
 exit 1
fi

# stop services
echo "making sure services are not running"
sudo systemctl stop lnd 2>/dev/null
sudo systemctl stop ${network}d 2>/dev/null

# editing network config files (hdd & admin user)
echo "edit ${network} config .."
# fix old lnd config file (that worked with switching comment)
sudo sed -i "s/^#testnet=.*/testnet=1/g" /mnt/hdd/app-data/${network}/${network}.conf
sudo sed -i "s/^#testnet=.*/testnet=1/g" /home/<USER>/.${network}/${network}.conf
# changes based on parameter
if [ "$1" = "testnet" ]; then
  echo "editing /mnt/hdd/app-data/${network}/${network}.conf"
  sudo sed -i "s/^testnet=.*/testnet=1/g" /mnt/hdd/app-data/${network}/${network}.conf
  echo "editing /home/<USER>/.${network}/${network}.conf"
  sudo sed -i "s/^testnet=.*/testnet=1/g" /home/<USER>/.${network}/${network}.conf
else
  echo "editing /mnt/hdd/app-data/${network}/${network}.conf"
  sudo sed -i "s/^testnet=.*/testnet=0/g" /mnt/hdd/app-data/${network}/${network}.conf
  echo "editing /home/<USER>/.${network}/${network}.conf"
  sudo sed -i "s/^testnet=.*/testnet=0/g" /home/<USER>/.${network}/${network}.conf
fi

# editing lnd config files (hdd & admin user)
echo "edit lightning config .."
# fix old lnd config file (that worked with switching comment)
sudo sed -i "s/^#bitcoin.testnet=.*/bitcoin.testnet=1/g" /mnt/hdd/app-data/lnd/lnd.conf
sudo sed -i "s/^#bitcoin.testnet=.*/bitcoin.testnet=1/g" /home/<USER>/.lnd/lnd.conf
# changes based on parameter
if [ "$1" = "testnet" ]; then
  echo "editing /mnt/hdd/app-data/lnd/lnd.conf"
  sudo sed -i "s/^${network}.mainnet.*/${network}.mainnet=0/g" /mnt/hdd/app-data/lnd/lnd.conf
  sudo sed -i "s/^${network}.testnet.*/${network}.testnet=1/g" /mnt/hdd/app-data/lnd/lnd.conf
  echo "editing /home/<USER>/.lnd/lnd.conf"
  sudo sed -i "s/^${network}.mainnet.*/${network}.mainnet=0/g" /home/<USER>/.lnd/lnd.conf
  sudo sed -i "s/^${network}.testnet.*/${network}.testnet=1/g" /home/<USER>/.lnd/lnd.conf
else
  echo "editing /mnt/hdd/app-data/lnd/lnd.conf"
  sudo sed -i "s/^${network}.mainnet.*/${network}.mainnet=1/g" /mnt/hdd/app-data/lnd/lnd.conf
  sudo sed -i "s/^${network}.testnet.*/${network}.testnet=0/g" /mnt/hdd/app-data/lnd/lnd.conf
  echo "editing /home/<USER>/.lnd/lnd.conf"
  sudo sed -i "s/^${network}.mainnet.*/${network}.mainnet=1/g" /home/<USER>/.lnd/lnd.conf
  sudo sed -i "s/^${network}.testnet.*/${network}.testnet=0/g" /home/<USER>/.lnd/lnd.conf
fi

# editing the raspi blitz config file
echo "editing /mnt/hdd/app-data/raspiblitz.conf"
if [ "$1" = "testnet" ]; then
  /home/<USER>/config.scripts/blitz.conf.sh set chain "test"
else
  /home/<USER>/config.scripts/blitz.conf.sh set chain "main"
fi

# edit RTL.conf (if active)
if [ "${rtlWebinterface}" = "on" ]; then
  echo "editing /home/<USER>/RTL/RTL.conf"
  sudo sed -i "s/^macroonPath=.*/macroonPath=\/mnt\/hdd\/lnd\/data\/chain\/${network}\/$1/g" /home/<USER>/RTL/RTL.conf
fi

# now a reboot is needed to load all services fresh
# starting up process will display chain sync
# ask user todo reboot
echo "OK - all configs changed to: $1"
echo "needs reboot to activate new setting"
