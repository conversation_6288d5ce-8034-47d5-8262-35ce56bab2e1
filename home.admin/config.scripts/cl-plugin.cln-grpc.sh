#!/bin/bash

# command info
if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "-help" ]; then
  echo
  echo "Install the cln-grpc plugin for CLN"
  echo "Usage:"
  echo "cl-plugin.cln-grpc.sh install - called by build_sdcard.sh"
  echo "cl-plugin.cln-grpc.sh on <testnet|mainnet|signet>"
  echo "cl-plugin.cln-grpc.sh off <testnet|mainnet|signet> <purge>"
  echo "cl-plugin.cln-grpc.sh status <testnet|mainnet|signet>"
  echo "cl-plugin.cln-grpc.sh update <source>"
  echo
  exit 1
fi

echo "# cl-plugin.cln-grpc.sh $*"

if [ "$2" = testnet ] || [ "$2" = signet ]; then
  NETWORK=$2
else
  NETWORK=mainnet
fi
source <(/home/<USER>/config.scripts/network.aliases.sh getvars cl $NETWORK)

# netprefix is:     "" |  t | s
# portprefix is:    "" |  1 | 3
PORT="${portprefix}4772"

function buildGRPCplugin() {
  echo "# - check the cln-grpc plugin"
  if [ ! -f /usr/local/libexec/c-lightning/plugins/cln-grpc ] || [ ! -f /home/<USER>/cl-plugins-available/cln-grpc ]; then
    # check if the source code is present
    if [ ! -d /home/<USER>/lightning/plugins/grpc-plugin ]; then
      echo "# - install Core Lightning ..."
      /home/<USER>/config.scripts/cl.install.sh install || exit 1
    fi
    if [ -f /usr/local/libexec/c-lightning/plugins/cln-grpc ]; then
      echo "# - cln-grpc plugin was installed"
    else
      echo "# install dependencies"
      sudo apt-get install protobuf-compiler -y
      echo "# rust for cln-grpc, includes rustfmt"
      sudo -u bitcoin curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sudo -u bitcoin sh -s -- -y
      cd /home/<USER>/lightning/plugins/grpc-plugin || exit 1
      echo "# build"
      sudo -u bitcoin /home/<USER>/.cargo/bin/cargo build --target-dir /home/<USER>/cln-grpc-build
      echo "# delete old dir or binary"
      sudo rm -rf /home/<USER>/cl-plugins-available/cln-grpc
      echo "# move to /home/<USER>/cl-plugins-available/"
      sudo mkdir -p /home/<USER>/cl-plugins-available
      sudo -u bitcoin mv /home/<USER>/cln-grpc-build/debug/cln-grpc /home/<USER>/cl-plugins-available/
    fi
  else
    echo "# - cln-grpc plugin was already built/installed"
  fi
  echo "# Cleaning"
  sudo rm -rf /home/<USER>/.rustup
  sudo rm -rf /home/<USER>/.cargo/
  sudo rm -rf /home/<USER>/.cache
  sudo rm -rf /home/<USER>/cln-grpc-build
}

function switchOn() {
  if ! $lightningcli_alias plugin list | grep "/home/<USER>/${netprefix}cl-plugins-enabled/cln-grpc"; then
    buildGRPCplugin

    if [ ! -f /usr/local/libexec/c-lightning/plugins/cln-grpc ]; then
      # symlink to plugin directory
      echo "# symlink cln-grpc to /home/<USER>/${netprefix}cl-plugins-enabled/"
      # delete old symlink
      sudo rm -f /home/<USER>/${netprefix}cl-plugins-enabled/cln-grpc
      sudo ln -s /home/<USER>/cl-plugins-available/cln-grpc /home/<USER>/${netprefix}cl-plugins-enabled/
    fi
    # blitz.conf.sh set [key] [value] [?conffile] <noquotes>
    /home/<USER>/config.scripts/blitz.conf.sh set "grpc-port" "${PORT}" "${CLCONF}" "noquotes"
    /home/<USER>/config.scripts/blitz.conf.sh set "${netprefix}clnGRPCport" "${PORT}"

    # firewall
    sudo ufw allow "${PORT}" comment "${netprefix}clnGRPCport"
    # Tor
    /home/<USER>/config.scripts/tor.onion-service.sh "${netprefix}clnGRPCport" "${PORT}" "${PORT}"
    source <(/home/<USER>/_cache.sh get state)
    if [ "${state}" == "ready" ]; then
      sudo systemctl restart ${netprefix}lightningd
    fi
    echo "# cl-plugin.cln-grpc.sh on --> done"

  else
    echo "# cl-plugin.cln-grpc.sh on --> already installed and running"
  fi
  exit 0
}

if [ "$1" = install ]; then
  buildGRPCplugin
  echo "# cl-plugin.cln-grpc.sh install --> done"
  exit 0

elif [ "$1" = status ]; then

  portActive=$(nc -vz 127.0.0.1 $PORT 2>&1 | grep -c "succeeded")
  echo "port=${PORT}"
  echo "portActive=${portActive}"
  exit 0

elif [ "$1" = on ]; then
  switchOn

elif [ "$1" = off ]; then
  sed -i "/^grpc-port/d" "${CLCONF}"
  sudo rm -rf /home/<USER>/${netprefix}cl-plugins-enabled/cln-grpc
  if [ "$(echo "$@" | grep -c purge)" -gt 0 ]; then
    sudo rm -rf /home/<USER>/cl-plugins-available/cln-grpc
  fi
  /home/<USER>/config.scripts/blitz.conf.sh set "${netprefix}clnGRPCport" "off"
  # firewall
  sudo ufw deny "${PORT}" comment "${netprefix}clnGRPCport"
  # Tor
  /home/<USER>/config.scripts/tor.onion-service.sh off "${netprefix}clnGRPCport"
  exit 0

elif [ "$1" = update ]; then
  if [ "$(echo "$@" | grep -c source)" -gt 0 ]; then
    cd /home/<USER>/lightning/ || exit 1
    sudo -u bitcoin git pull
  fi
  sudo rm -rf /home/<USER>/cl-plugins-available/cln-grpc
  buildGRPCplugin
  sudo systemctl stop ${netprefix}lightningd
  switchOn
  echo "# cl-plugin.cln-grpc.sh update  --> done"

else
  echo "FAIL - Unknown Parameter $1"
  exit 1
fi
