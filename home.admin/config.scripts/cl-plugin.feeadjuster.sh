#!/bin/bash

# command info
if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "-help" ];then
  echo
  echo "Install the feeadjuster plugin for Core Lightning"
  echo "Usage:"
  echo "cl-plugin.feeadjuster.sh [on|off] <testnet|mainnet|signet>"
  echo
  exit 1
fi

source <(/home/<USER>/config.scripts/network.aliases.sh getvars cl $2)
plugin="feeadjuster"

if [ "$1" = "on" ];then

  if [ ! -f "/home/<USER>/cl-plugins-available/plugins/${plugin}/${plugin}.py" ]; then
    cd /home/<USER>/cl-plugins-available || exit 1
    sudo -u bitcoin git clone https://github.com/lightningd/plugins.git
    sudo -u bitcoin pip config set global.break-system-packages true
    sudo -u bitcoin pip install -r /home/<USER>/cl-plugins-available/plugins/${plugin}/requirements.txt
  fi
  if [ ! -L /home/<USER>/${netprefix}cl-plugins-enabled/${plugin}.py ];then
    sudo ln -s /home/<USER>/cl-plugins-available/plugins/${plugin}/${plugin}.py \
               /home/<USER>/${netprefix}cl-plugins-enabled
    sudo chmod +x /home/<USER>/cl-plugins-available/plugins/${plugin}/${plugin}.py
  fi

  # setting value in raspi blitz config
  /home/<USER>/config.scripts/blitz.conf.sh set ${netprefix}feeadjuster "on"

  source <(/home/<USER>/_cache.sh get state)
  if [ "${state}" == "ready" ] && [ "$3" != "norestart" ]; then
    echo "# Start ${netprefix}${plugin}"
    $lightningcli_alias plugin start /home/<USER>/cl-plugins-enabled/${plugin}.py
  fi

fi

if [ "$1" = "off" ];then

  echo "Stop the ${plugin}"
  $lightningcli_alias plugin stop home/bitcoin/${netprefix}cl-plugins-enabled/${plugin}.py

  echo "# delete symlink"
  sudo rm -rf /home/<USER>/${netprefix}cl-plugins-enabled/${plugin}.py
  
  echo "# Edit ${CLCONF}"
  sudo sed -i "/^feeadjuster/d" ${CLCONF}

  # setting value in raspi blitz config
  /home/<USER>/config.scripts/blitz.conf.sh set ${netprefix}feeadjuster "off"

  echo "# The ${plugin} was uninstalled"
fi