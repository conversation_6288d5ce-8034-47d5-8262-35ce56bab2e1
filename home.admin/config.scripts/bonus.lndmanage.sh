#!/bin/bash

# command info
if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "-help" ]; then
 echo "config script to install or uninstall lndmanage"
 echo "bonus.lndmanage.sh [on|off|menu]"
 exit 1
fi

# set version of LND manage to install
# https://github.com/bitromortac/lndmanage/releases
lndmanageVersion="0.16.0"
pgpKeyDownload="https://github.com/bitromortac.gpg"
gpgFingerprint="0453B9F5071261A40FDB34181965063FC13BEBE2"

source /mnt/hdd/app-data/raspiblitz.conf

# show info menu
if [ "$1" = "menu" ]; then
  dialog --title " Info lndmanage " --msgbox "\n\
Usage: https://github.com/bitromortac/lndmanage/blob/master/README.md or
lndmanage --help.\n
To start type: 'manage' in the command line.
" 9 75
  exit 0
fi

# install
if [ "$1" = "1" ] || [ "$1" = "on" ]; then

  directoryExists=$(sudo ls /home/<USER>/lndmanage 2>/dev/null | wc -l)
  if [ ${directoryExists} -gt 0 ]; then
    echo "# FAIL - LNDMANAGE already installed"
    sleep 3
    exit 1
  fi
  
  echo "*** INSTALL LNDMANAGE ***"

  # make sure needed os dependencies are installed
  sudo apt-get install -y libatlas-base-dev

  # prepare directory
  mkdir /home/<USER>/lndmanage 2>/dev/null
  sudo chown admin:admin /home/<USER>/lndmanage

  echo "# downloading files ..."
  cd /home/<USER>/lndmanage
  sudo -u admin wget -N https://github.com/bitromortac/lndmanage/releases/download/v${lndmanageVersion}/lndmanage-${lndmanageVersion}-py3-none-any.whl
  sudo -u admin wget -N https://github.com/bitromortac/lndmanage/releases/download/v${lndmanageVersion}/lndmanage-${lndmanageVersion}-py3-none-any.whl.asc
  sudo -u admin wget -N ${pgpKeyDownload} -O sigingkey.gpg

  echo "# checking signing keys ..."
  gpg --import sigingkey.gpg
  verifyResult=$(LANG=en_US.utf8; gpg --verify lndmanage-${lndmanageVersion}-py3-none-any.whl.asc 2>&1)
  goodSignature=$(echo ${verifyResult} | grep 'Good signature' -c)
  correctKey=$(echo ${verifyResult} | tr -d " \t\n\r" | grep "${gpgFingerprint}" -c)
  echo "goodSignature='${goodSignature}'"
  echo "correctKey='${correctKey}'"
  if [ ${goodSignature} -gt 0 ] && [ ${correctKey} -gt 0 ]; then
    echo "# OK signature is valid"
  else
    echo "error='invalid signature'"
    sudo rm -rf /home/<USER>/lndmanage
    sleep 5
    exit 1
  fi

  echo "# installing ..."
  python3 -m venv venv
  source /home/<USER>/lndmanage/venv/bin/activate
  python3 -m pip install lndmanage-${lndmanageVersion}-py3-none-any.whl

  # get build dependencies
  # python3 -m pip install --upgrade pip wheel setuptools
  # install lndmanage
  # python3 -m pip install lndmanage==0.11.0

  # check if install was successful
  if [ $(python3 -m pip list | grep -c "lndmanage") -eq 0 ]; then
    echo
    echo "## FAIL --> Was not able to install LNDMANAGE"
    echo "## Maybe because of internet network issues - try again later."
    sudo rm -rf /home/<USER>/lndmanage
    sleep 5
    exit 1
  fi

  # setting value in raspi blitz config
  /home/<USER>/config.scripts/blitz.conf.sh set lndmanage "on"
  echo "#######################################################################"
  echo "# OK install done"
  echo "#######################################################################"
  echo "# To start type: 'manage' in the command line."
  echo "# To exit the venv - type 'deactivate' and press ENTER"
  echo "# usage: https://github.com/bitromortac/lndmanage/blob/master/README.md"
  echo "# usage: lndmanage --help"
  exit 0
fi

# switch off
if [ "$1" = "0" ] || [ "$1" = "off" ]; then

  # setting value in raspi blitz config
  /home/<USER>/config.scripts/blitz.conf.sh set lndmanage "off"
  
  echo "*** REMOVING LNDMANAGE ***"
  sudo rm -rf /home/<USER>/lndmanage
  echo "# OK, lndmanage is removed."
  exit 0

fi

echo "FAIL - Unknown Parameter $1"
exit 1
