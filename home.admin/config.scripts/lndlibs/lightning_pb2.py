# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lightning.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0flightning.proto\x12\x05lnrpc\"B\n\x1bLookupHtlcResolutionRequest\x12\x0f\n\x07\x63han_id\x18\x01 \x01(\x04\x12\x12\n\nhtlc_index\x18\x02 \x01(\x04\"A\n\x1cLookupHtlcResolutionResponse\x12\x0f\n\x07settled\x18\x01 \x01(\x08\x12\x10\n\x08offchain\x18\x02 \x01(\x08\" \n\x1eSubscribeCustomMessagesRequest\"9\n\rCustomMessage\x12\x0c\n\x04peer\x18\x01 \x01(\x0c\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\"D\n\x18SendCustomMessageRequest\x12\x0c\n\x04peer\x18\x01 \x01(\x0c\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\"\x1b\n\x19SendCustomMessageResponse\"\xa2\x01\n\x04Utxo\x12(\n\x0c\x61\x64\x64ress_type\x18\x01 \x01(\x0e\x32\x12.lnrpc.AddressType\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\x12\x12\n\namount_sat\x18\x03 \x01(\x03\x12\x11\n\tpk_script\x18\x04 \x01(\t\x12!\n\x08outpoint\x18\x05 \x01(\x0b\x32\x0f.lnrpc.OutPoint\x12\x15\n\rconfirmations\x18\x06 \x01(\x03\"\x9e\x01\n\x0cOutputDetail\x12,\n\x0boutput_type\x18\x01 \x01(\x0e\x32\x17.lnrpc.OutputScriptType\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\x12\x11\n\tpk_script\x18\x03 \x01(\t\x12\x14\n\x0coutput_index\x18\x04 \x01(\x03\x12\x0e\n\x06\x61mount\x18\x05 \x01(\x03\x12\x16\n\x0eis_our_address\x18\x06 \x01(\x08\"\xbc\x02\n\x0bTransaction\x12\x0f\n\x07tx_hash\x18\x01 \x01(\t\x12\x0e\n\x06\x61mount\x18\x02 \x01(\x03\x12\x19\n\x11num_confirmations\x18\x03 \x01(\x05\x12\x12\n\nblock_hash\x18\x04 \x01(\t\x12\x14\n\x0c\x62lock_height\x18\x05 \x01(\x05\x12\x12\n\ntime_stamp\x18\x06 \x01(\x03\x12\x12\n\ntotal_fees\x18\x07 \x01(\x03\x12\x1a\n\x0e\x64\x65st_addresses\x18\x08 \x03(\tB\x02\x18\x01\x12+\n\x0eoutput_details\x18\x0b \x03(\x0b\x32\x13.lnrpc.OutputDetail\x12\x12\n\nraw_tx_hex\x18\t \x01(\t\x12\r\n\x05label\x18\n \x01(\t\x12\x33\n\x12previous_outpoints\x18\x0c \x03(\x0b\x32\x17.lnrpc.PreviousOutPoint\"S\n\x16GetTransactionsRequest\x12\x14\n\x0cstart_height\x18\x01 \x01(\x05\x12\x12\n\nend_height\x18\x02 \x01(\x05\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\">\n\x12TransactionDetails\x12(\n\x0ctransactions\x18\x01 \x03(\x0b\x32\x12.lnrpc.Transaction\"M\n\x08\x46\x65\x65Limit\x12\x0f\n\x05\x66ixed\x18\x01 \x01(\x03H\x00\x12\x14\n\nfixed_msat\x18\x03 \x01(\x03H\x00\x12\x11\n\x07percent\x18\x02 \x01(\x03H\x00\x42\x07\n\x05limit\"\x8a\x04\n\x0bSendRequest\x12\x0c\n\x04\x64\x65st\x18\x01 \x01(\x0c\x12\x17\n\x0b\x64\x65st_string\x18\x02 \x01(\tB\x02\x18\x01\x12\x0b\n\x03\x61mt\x18\x03 \x01(\x03\x12\x10\n\x08\x61mt_msat\x18\x0c \x01(\x03\x12\x14\n\x0cpayment_hash\x18\x04 \x01(\x0c\x12\x1f\n\x13payment_hash_string\x18\x05 \x01(\tB\x02\x18\x01\x12\x17\n\x0fpayment_request\x18\x06 \x01(\t\x12\x18\n\x10\x66inal_cltv_delta\x18\x07 \x01(\x05\x12\"\n\tfee_limit\x18\x08 \x01(\x0b\x32\x0f.lnrpc.FeeLimit\x12\x1c\n\x10outgoing_chan_id\x18\t \x01(\x04\x42\x02\x30\x01\x12\x17\n\x0flast_hop_pubkey\x18\r \x01(\x0c\x12\x12\n\ncltv_limit\x18\n \x01(\r\x12\x46\n\x13\x64\x65st_custom_records\x18\x0b \x03(\x0b\x32).lnrpc.SendRequest.DestCustomRecordsEntry\x12\x1a\n\x12\x61llow_self_payment\x18\x0e \x01(\x08\x12(\n\rdest_features\x18\x0f \x03(\x0e\x32\x11.lnrpc.FeatureBit\x12\x14\n\x0cpayment_addr\x18\x10 \x01(\x0c\x1a\x38\n\x16\x44\x65stCustomRecordsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\"z\n\x0cSendResponse\x12\x15\n\rpayment_error\x18\x01 \x01(\t\x12\x18\n\x10payment_preimage\x18\x02 \x01(\x0c\x12#\n\rpayment_route\x18\x03 \x01(\x0b\x32\x0c.lnrpc.Route\x12\x14\n\x0cpayment_hash\x18\x04 \x01(\x0c\"n\n\x12SendToRouteRequest\x12\x14\n\x0cpayment_hash\x18\x01 \x01(\x0c\x12\x1f\n\x13payment_hash_string\x18\x02 \x01(\tB\x02\x18\x01\x12\x1b\n\x05route\x18\x04 \x01(\x0b\x32\x0c.lnrpc.RouteJ\x04\x08\x03\x10\x04\"\x98\x03\n\x14\x43hannelAcceptRequest\x12\x13\n\x0bnode_pubkey\x18\x01 \x01(\x0c\x12\x12\n\nchain_hash\x18\x02 \x01(\x0c\x12\x17\n\x0fpending_chan_id\x18\x03 \x01(\x0c\x12\x13\n\x0b\x66unding_amt\x18\x04 \x01(\x04\x12\x10\n\x08push_amt\x18\x05 \x01(\x04\x12\x12\n\ndust_limit\x18\x06 \x01(\x04\x12\x1b\n\x13max_value_in_flight\x18\x07 \x01(\x04\x12\x17\n\x0f\x63hannel_reserve\x18\x08 \x01(\x04\x12\x10\n\x08min_htlc\x18\t \x01(\x04\x12\x12\n\nfee_per_kw\x18\n \x01(\x04\x12\x11\n\tcsv_delay\x18\x0b \x01(\r\x12\x1a\n\x12max_accepted_htlcs\x18\x0c \x01(\r\x12\x15\n\rchannel_flags\x18\r \x01(\r\x12.\n\x0f\x63ommitment_type\x18\x0e \x01(\x0e\x32\x15.lnrpc.CommitmentType\x12\x17\n\x0fwants_zero_conf\x18\x0f \x01(\x08\x12\x18\n\x10wants_scid_alias\x18\x10 \x01(\x08\"\x87\x02\n\x15\x43hannelAcceptResponse\x12\x0e\n\x06\x61\x63\x63\x65pt\x18\x01 \x01(\x08\x12\x17\n\x0fpending_chan_id\x18\x02 \x01(\x0c\x12\r\n\x05\x65rror\x18\x03 \x01(\t\x12\x18\n\x10upfront_shutdown\x18\x04 \x01(\t\x12\x11\n\tcsv_delay\x18\x05 \x01(\r\x12\x13\n\x0breserve_sat\x18\x06 \x01(\x04\x12\x1a\n\x12in_flight_max_msat\x18\x07 \x01(\x04\x12\x16\n\x0emax_htlc_count\x18\x08 \x01(\r\x12\x13\n\x0bmin_htlc_in\x18\t \x01(\x04\x12\x18\n\x10min_accept_depth\x18\n \x01(\r\x12\x11\n\tzero_conf\x18\x0b \x01(\x08\"n\n\x0c\x43hannelPoint\x12\x1c\n\x12\x66unding_txid_bytes\x18\x01 \x01(\x0cH\x00\x12\x1a\n\x10\x66unding_txid_str\x18\x02 \x01(\tH\x00\x12\x14\n\x0coutput_index\x18\x03 \x01(\rB\x0e\n\x0c\x66unding_txid\"F\n\x08OutPoint\x12\x12\n\ntxid_bytes\x18\x01 \x01(\x0c\x12\x10\n\x08txid_str\x18\x02 \x01(\t\x12\x14\n\x0coutput_index\x18\x03 \x01(\r\";\n\x10PreviousOutPoint\x12\x10\n\x08outpoint\x18\x01 \x01(\t\x12\x15\n\ris_our_output\x18\x02 \x01(\x08\"0\n\x10LightningAddress\x12\x0e\n\x06pubkey\x18\x01 \x01(\t\x12\x0c\n\x04host\x18\x02 \x01(\t\"\xcf\x01\n\x12\x45stimateFeeRequest\x12\x41\n\x0c\x41\x64\x64rToAmount\x18\x01 \x03(\x0b\x32+.lnrpc.EstimateFeeRequest.AddrToAmountEntry\x12\x13\n\x0btarget_conf\x18\x02 \x01(\x05\x12\x11\n\tmin_confs\x18\x03 \x01(\x05\x12\x19\n\x11spend_unconfirmed\x18\x04 \x01(\x08\x1a\x33\n\x11\x41\x64\x64rToAmountEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\"_\n\x13\x45stimateFeeResponse\x12\x0f\n\x07\x66\x65\x65_sat\x18\x01 \x01(\x03\x12 \n\x14\x66\x65\x65rate_sat_per_byte\x18\x02 \x01(\x03\x42\x02\x18\x01\x12\x15\n\rsat_per_vbyte\x18\x03 \x01(\x04\"\x89\x02\n\x0fSendManyRequest\x12>\n\x0c\x41\x64\x64rToAmount\x18\x01 \x03(\x0b\x32(.lnrpc.SendManyRequest.AddrToAmountEntry\x12\x13\n\x0btarget_conf\x18\x03 \x01(\x05\x12\x15\n\rsat_per_vbyte\x18\x04 \x01(\x04\x12\x18\n\x0csat_per_byte\x18\x05 \x01(\x03\x42\x02\x18\x01\x12\r\n\x05label\x18\x06 \x01(\t\x12\x11\n\tmin_confs\x18\x07 \x01(\x05\x12\x19\n\x11spend_unconfirmed\x18\x08 \x01(\x08\x1a\x33\n\x11\x41\x64\x64rToAmountEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\" \n\x10SendManyResponse\x12\x0c\n\x04txid\x18\x01 \x01(\t\"\xc5\x01\n\x10SendCoinsRequest\x12\x0c\n\x04\x61\x64\x64r\x18\x01 \x01(\t\x12\x0e\n\x06\x61mount\x18\x02 \x01(\x03\x12\x13\n\x0btarget_conf\x18\x03 \x01(\x05\x12\x15\n\rsat_per_vbyte\x18\x04 \x01(\x04\x12\x18\n\x0csat_per_byte\x18\x05 \x01(\x03\x42\x02\x18\x01\x12\x10\n\x08send_all\x18\x06 \x01(\x08\x12\r\n\x05label\x18\x07 \x01(\t\x12\x11\n\tmin_confs\x18\x08 \x01(\x05\x12\x19\n\x11spend_unconfirmed\x18\t \x01(\x08\"!\n\x11SendCoinsResponse\x12\x0c\n\x04txid\x18\x01 \x01(\t\"K\n\x12ListUnspentRequest\x12\x11\n\tmin_confs\x18\x01 \x01(\x05\x12\x11\n\tmax_confs\x18\x02 \x01(\x05\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\"1\n\x13ListUnspentResponse\x12\x1a\n\x05utxos\x18\x01 \x03(\x0b\x32\x0b.lnrpc.Utxo\"F\n\x11NewAddressRequest\x12 \n\x04type\x18\x01 \x01(\x0e\x32\x12.lnrpc.AddressType\x12\x0f\n\x07\x61\x63\x63ount\x18\x02 \x01(\t\"%\n\x12NewAddressResponse\x12\x0f\n\x07\x61\x64\x64ress\x18\x01 \x01(\t\"6\n\x12SignMessageRequest\x12\x0b\n\x03msg\x18\x01 \x01(\x0c\x12\x13\n\x0bsingle_hash\x18\x02 \x01(\x08\"(\n\x13SignMessageResponse\x12\x11\n\tsignature\x18\x01 \x01(\t\"6\n\x14VerifyMessageRequest\x12\x0b\n\x03msg\x18\x01 \x01(\x0c\x12\x11\n\tsignature\x18\x02 \x01(\t\"6\n\x15VerifyMessageResponse\x12\r\n\x05valid\x18\x01 \x01(\x08\x12\x0e\n\x06pubkey\x18\x02 \x01(\t\"Z\n\x12\x43onnectPeerRequest\x12%\n\x04\x61\x64\x64r\x18\x01 \x01(\x0b\x32\x17.lnrpc.LightningAddress\x12\x0c\n\x04perm\x18\x02 \x01(\x08\x12\x0f\n\x07timeout\x18\x03 \x01(\x04\"\x15\n\x13\x43onnectPeerResponse\"(\n\x15\x44isconnectPeerRequest\x12\x0f\n\x07pub_key\x18\x01 \x01(\t\"\x18\n\x16\x44isconnectPeerResponse\"\xa5\x01\n\x04HTLC\x12\x10\n\x08incoming\x18\x01 \x01(\x08\x12\x0e\n\x06\x61mount\x18\x02 \x01(\x03\x12\x11\n\thash_lock\x18\x03 \x01(\x0c\x12\x19\n\x11\x65xpiration_height\x18\x04 \x01(\r\x12\x12\n\nhtlc_index\x18\x05 \x01(\x04\x12\x1a\n\x12\x66orwarding_channel\x18\x06 \x01(\x04\x12\x1d\n\x15\x66orwarding_htlc_index\x18\x07 \x01(\x04\"\xaa\x01\n\x12\x43hannelConstraints\x12\x11\n\tcsv_delay\x18\x01 \x01(\r\x12\x18\n\x10\x63han_reserve_sat\x18\x02 \x01(\x04\x12\x16\n\x0e\x64ust_limit_sat\x18\x03 \x01(\x04\x12\x1c\n\x14max_pending_amt_msat\x18\x04 \x01(\x04\x12\x15\n\rmin_htlc_msat\x18\x05 \x01(\x04\x12\x1a\n\x12max_accepted_htlcs\x18\x06 \x01(\r\"\xb9\x07\n\x07\x43hannel\x12\x0e\n\x06\x61\x63tive\x18\x01 \x01(\x08\x12\x15\n\rremote_pubkey\x18\x02 \x01(\t\x12\x15\n\rchannel_point\x18\x03 \x01(\t\x12\x13\n\x07\x63han_id\x18\x04 \x01(\x04\x42\x02\x30\x01\x12\x10\n\x08\x63\x61pacity\x18\x05 \x01(\x03\x12\x15\n\rlocal_balance\x18\x06 \x01(\x03\x12\x16\n\x0eremote_balance\x18\x07 \x01(\x03\x12\x12\n\ncommit_fee\x18\x08 \x01(\x03\x12\x15\n\rcommit_weight\x18\t \x01(\x03\x12\x12\n\nfee_per_kw\x18\n \x01(\x03\x12\x19\n\x11unsettled_balance\x18\x0b \x01(\x03\x12\x1b\n\x13total_satoshis_sent\x18\x0c \x01(\x03\x12\x1f\n\x17total_satoshis_received\x18\r \x01(\x03\x12\x13\n\x0bnum_updates\x18\x0e \x01(\x04\x12\"\n\rpending_htlcs\x18\x0f \x03(\x0b\x32\x0b.lnrpc.HTLC\x12\x15\n\tcsv_delay\x18\x10 \x01(\rB\x02\x18\x01\x12\x0f\n\x07private\x18\x11 \x01(\x08\x12\x11\n\tinitiator\x18\x12 \x01(\x08\x12\x19\n\x11\x63han_status_flags\x18\x13 \x01(\t\x12\"\n\x16local_chan_reserve_sat\x18\x14 \x01(\x03\x42\x02\x18\x01\x12#\n\x17remote_chan_reserve_sat\x18\x15 \x01(\x03\x42\x02\x18\x01\x12\x1d\n\x11static_remote_key\x18\x16 \x01(\x08\x42\x02\x18\x01\x12.\n\x0f\x63ommitment_type\x18\x1a \x01(\x0e\x32\x15.lnrpc.CommitmentType\x12\x10\n\x08lifetime\x18\x17 \x01(\x03\x12\x0e\n\x06uptime\x18\x18 \x01(\x03\x12\x15\n\rclose_address\x18\x19 \x01(\t\x12\x17\n\x0fpush_amount_sat\x18\x1b \x01(\x04\x12\x13\n\x0bthaw_height\x18\x1c \x01(\r\x12\x34\n\x11local_constraints\x18\x1d \x01(\x0b\x32\x19.lnrpc.ChannelConstraints\x12\x35\n\x12remote_constraints\x18\x1e \x01(\x0b\x32\x19.lnrpc.ChannelConstraints\x12\x13\n\x0b\x61lias_scids\x18\x1f \x03(\x04\x12\x11\n\tzero_conf\x18  \x01(\x08\x12 \n\x18zero_conf_confirmed_scid\x18! \x01(\x04\x12\x12\n\npeer_alias\x18\" \x01(\t\x12\x1b\n\x0fpeer_scid_alias\x18# \x01(\x04\x42\x02\x30\x01\x12\x0c\n\x04memo\x18$ \x01(\t\"\x95\x01\n\x13ListChannelsRequest\x12\x13\n\x0b\x61\x63tive_only\x18\x01 \x01(\x08\x12\x15\n\rinactive_only\x18\x02 \x01(\x08\x12\x13\n\x0bpublic_only\x18\x03 \x01(\x08\x12\x14\n\x0cprivate_only\x18\x04 \x01(\x08\x12\x0c\n\x04peer\x18\x05 \x01(\x0c\x12\x19\n\x11peer_alias_lookup\x18\x06 \x01(\x08\"8\n\x14ListChannelsResponse\x12 \n\x08\x63hannels\x18\x0b \x03(\x0b\x32\x0e.lnrpc.Channel\".\n\x08\x41liasMap\x12\x11\n\tbase_scid\x18\x01 \x01(\x04\x12\x0f\n\x07\x61liases\x18\x02 \x03(\x04\"\x14\n\x12ListAliasesRequest\":\n\x13ListAliasesResponse\x12#\n\nalias_maps\x18\x01 \x03(\x0b\x32\x0f.lnrpc.AliasMap\"\xe4\x04\n\x13\x43hannelCloseSummary\x12\x15\n\rchannel_point\x18\x01 \x01(\t\x12\x13\n\x07\x63han_id\x18\x02 \x01(\x04\x42\x02\x30\x01\x12\x12\n\nchain_hash\x18\x03 \x01(\t\x12\x17\n\x0f\x63losing_tx_hash\x18\x04 \x01(\t\x12\x15\n\rremote_pubkey\x18\x05 \x01(\t\x12\x10\n\x08\x63\x61pacity\x18\x06 \x01(\x03\x12\x14\n\x0c\x63lose_height\x18\x07 \x01(\r\x12\x17\n\x0fsettled_balance\x18\x08 \x01(\x03\x12\x1b\n\x13time_locked_balance\x18\t \x01(\x03\x12:\n\nclose_type\x18\n \x01(\x0e\x32&.lnrpc.ChannelCloseSummary.ClosureType\x12(\n\x0eopen_initiator\x18\x0b \x01(\x0e\x32\x10.lnrpc.Initiator\x12)\n\x0f\x63lose_initiator\x18\x0c \x01(\x0e\x32\x10.lnrpc.Initiator\x12&\n\x0bresolutions\x18\r \x03(\x0b\x32\x11.lnrpc.Resolution\x12\x13\n\x0b\x61lias_scids\x18\x0e \x03(\x04\x12$\n\x18zero_conf_confirmed_scid\x18\x0f \x01(\x04\x42\x02\x30\x01\"\x8a\x01\n\x0b\x43losureType\x12\x15\n\x11\x43OOPERATIVE_CLOSE\x10\x00\x12\x15\n\x11LOCAL_FORCE_CLOSE\x10\x01\x12\x16\n\x12REMOTE_FORCE_CLOSE\x10\x02\x12\x10\n\x0c\x42REACH_CLOSE\x10\x03\x12\x14\n\x10\x46UNDING_CANCELED\x10\x04\x12\r\n\tABANDONED\x10\x05\"\xb2\x01\n\nResolution\x12.\n\x0fresolution_type\x18\x01 \x01(\x0e\x32\x15.lnrpc.ResolutionType\x12)\n\x07outcome\x18\x02 \x01(\x0e\x32\x18.lnrpc.ResolutionOutcome\x12!\n\x08outpoint\x18\x03 \x01(\x0b\x32\x0f.lnrpc.OutPoint\x12\x12\n\namount_sat\x18\x04 \x01(\x04\x12\x12\n\nsweep_txid\x18\x05 \x01(\t\"\x94\x01\n\x15\x43losedChannelsRequest\x12\x13\n\x0b\x63ooperative\x18\x01 \x01(\x08\x12\x13\n\x0blocal_force\x18\x02 \x01(\x08\x12\x14\n\x0cremote_force\x18\x03 \x01(\x08\x12\x0e\n\x06\x62reach\x18\x04 \x01(\x08\x12\x18\n\x10\x66unding_canceled\x18\x05 \x01(\x08\x12\x11\n\tabandoned\x18\x06 \x01(\x08\"F\n\x16\x43losedChannelsResponse\x12,\n\x08\x63hannels\x18\x01 \x03(\x0b\x32\x1a.lnrpc.ChannelCloseSummary\"\xef\x03\n\x04Peer\x12\x0f\n\x07pub_key\x18\x01 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x03 \x01(\t\x12\x12\n\nbytes_sent\x18\x04 \x01(\x04\x12\x12\n\nbytes_recv\x18\x05 \x01(\x04\x12\x10\n\x08sat_sent\x18\x06 \x01(\x03\x12\x10\n\x08sat_recv\x18\x07 \x01(\x03\x12\x0f\n\x07inbound\x18\x08 \x01(\x08\x12\x11\n\tping_time\x18\t \x01(\x03\x12\'\n\tsync_type\x18\n \x01(\x0e\x32\x14.lnrpc.Peer.SyncType\x12+\n\x08\x66\x65\x61tures\x18\x0b \x03(\x0b\x32\x19.lnrpc.Peer.FeaturesEntry\x12\'\n\x06\x65rrors\x18\x0c \x03(\x0b\x32\x17.lnrpc.TimestampedError\x12\x12\n\nflap_count\x18\r \x01(\x05\x12\x14\n\x0clast_flap_ns\x18\x0e \x01(\x03\x12\x19\n\x11last_ping_payload\x18\x0f \x01(\x0c\x1a?\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x0e.lnrpc.Feature:\x02\x38\x01\"P\n\x08SyncType\x12\x10\n\x0cUNKNOWN_SYNC\x10\x00\x12\x0f\n\x0b\x41\x43TIVE_SYNC\x10\x01\x12\x10\n\x0cPASSIVE_SYNC\x10\x02\x12\x0f\n\x0bPINNED_SYNC\x10\x03\"4\n\x10TimestampedError\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"(\n\x10ListPeersRequest\x12\x14\n\x0clatest_error\x18\x01 \x01(\x08\"/\n\x11ListPeersResponse\x12\x1a\n\x05peers\x18\x01 \x03(\x0b\x32\x0b.lnrpc.Peer\"\x17\n\x15PeerEventSubscription\"v\n\tPeerEvent\x12\x0f\n\x07pub_key\x18\x01 \x01(\t\x12(\n\x04type\x18\x02 \x01(\x0e\x32\x1a.lnrpc.PeerEvent.EventType\".\n\tEventType\x12\x0f\n\x0bPEER_ONLINE\x10\x00\x12\x10\n\x0cPEER_OFFLINE\x10\x01\"\x10\n\x0eGetInfoRequest\"\xe2\x04\n\x0fGetInfoResponse\x12\x0f\n\x07version\x18\x0e \x01(\t\x12\x13\n\x0b\x63ommit_hash\x18\x14 \x01(\t\x12\x17\n\x0fidentity_pubkey\x18\x01 \x01(\t\x12\r\n\x05\x61lias\x18\x02 \x01(\t\x12\r\n\x05\x63olor\x18\x11 \x01(\t\x12\x1c\n\x14num_pending_channels\x18\x03 \x01(\r\x12\x1b\n\x13num_active_channels\x18\x04 \x01(\r\x12\x1d\n\x15num_inactive_channels\x18\x0f \x01(\r\x12\x11\n\tnum_peers\x18\x05 \x01(\r\x12\x14\n\x0c\x62lock_height\x18\x06 \x01(\r\x12\x12\n\nblock_hash\x18\x08 \x01(\t\x12\x1d\n\x15\x62\x65st_header_timestamp\x18\r \x01(\x03\x12\x17\n\x0fsynced_to_chain\x18\t \x01(\x08\x12\x17\n\x0fsynced_to_graph\x18\x12 \x01(\x08\x12\x13\n\x07testnet\x18\n \x01(\x08\x42\x02\x18\x01\x12 \n\x06\x63hains\x18\x10 \x03(\x0b\x32\x0c.lnrpc.ChainB\x02\x18\x01\x12\x0c\n\x04uris\x18\x0c \x03(\t\x12\x36\n\x08\x66\x65\x61tures\x18\x13 \x03(\x0b\x32$.lnrpc.GetInfoResponse.FeaturesEntry\x12 \n\x18require_htlc_interceptor\x18\x15 \x01(\x08\x12$\n\x1cstore_final_htlc_resolutions\x18\x16 \x01(\x08\x1a?\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x0e.lnrpc.Feature:\x02\x38\x01J\x04\x08\x0b\x10\x0c\"\x18\n\x16GetRecoveryInfoRequest\"]\n\x17GetRecoveryInfoResponse\x12\x15\n\rrecovery_mode\x18\x01 \x01(\x08\x12\x19\n\x11recovery_finished\x18\x02 \x01(\x08\x12\x10\n\x08progress\x18\x03 \x01(\x01\"+\n\x05\x43hain\x12\x11\n\x05\x63hain\x18\x01 \x01(\tB\x02\x18\x01\x12\x0f\n\x07network\x18\x02 \x01(\t\"U\n\x12\x43onfirmationUpdate\x12\x11\n\tblock_sha\x18\x01 \x01(\x0c\x12\x14\n\x0c\x62lock_height\x18\x02 \x01(\x05\x12\x16\n\x0enum_confs_left\x18\x03 \x01(\r\"?\n\x11\x43hannelOpenUpdate\x12*\n\rchannel_point\x18\x01 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\";\n\x12\x43hannelCloseUpdate\x12\x14\n\x0c\x63losing_txid\x18\x01 \x01(\x0c\x12\x0f\n\x07success\x18\x02 \x01(\x08\"\xcb\x01\n\x13\x43loseChannelRequest\x12*\n\rchannel_point\x18\x01 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\x12\r\n\x05\x66orce\x18\x02 \x01(\x08\x12\x13\n\x0btarget_conf\x18\x03 \x01(\x05\x12\x18\n\x0csat_per_byte\x18\x04 \x01(\x03\x42\x02\x18\x01\x12\x18\n\x10\x64\x65livery_address\x18\x05 \x01(\t\x12\x15\n\rsat_per_vbyte\x18\x06 \x01(\x04\x12\x19\n\x11max_fee_per_vbyte\x18\x07 \x01(\x04\"}\n\x11\x43loseStatusUpdate\x12-\n\rclose_pending\x18\x01 \x01(\x0b\x32\x14.lnrpc.PendingUpdateH\x00\x12/\n\nchan_close\x18\x03 \x01(\x0b\x32\x19.lnrpc.ChannelCloseUpdateH\x00\x42\x08\n\x06update\"3\n\rPendingUpdate\x12\x0c\n\x04txid\x18\x01 \x01(\x0c\x12\x14\n\x0coutput_index\x18\x02 \x01(\r\"T\n\x13ReadyForPsbtFunding\x12\x17\n\x0f\x66unding_address\x18\x01 \x01(\t\x12\x16\n\x0e\x66unding_amount\x18\x02 \x01(\x03\x12\x0c\n\x04psbt\x18\x03 \x01(\x0c\"\xad\x01\n\x17\x42\x61tchOpenChannelRequest\x12)\n\x08\x63hannels\x18\x01 \x03(\x0b\x32\x17.lnrpc.BatchOpenChannel\x12\x13\n\x0btarget_conf\x18\x02 \x01(\x05\x12\x15\n\rsat_per_vbyte\x18\x03 \x01(\x03\x12\x11\n\tmin_confs\x18\x04 \x01(\x05\x12\x19\n\x11spend_unconfirmed\x18\x05 \x01(\x08\x12\r\n\x05label\x18\x06 \x01(\t\"\xf9\x03\n\x10\x42\x61tchOpenChannel\x12\x13\n\x0bnode_pubkey\x18\x01 \x01(\x0c\x12\x1c\n\x14local_funding_amount\x18\x02 \x01(\x03\x12\x10\n\x08push_sat\x18\x03 \x01(\x03\x12\x0f\n\x07private\x18\x04 \x01(\x08\x12\x15\n\rmin_htlc_msat\x18\x05 \x01(\x03\x12\x18\n\x10remote_csv_delay\x18\x06 \x01(\r\x12\x15\n\rclose_address\x18\x07 \x01(\t\x12\x17\n\x0fpending_chan_id\x18\x08 \x01(\x0c\x12.\n\x0f\x63ommitment_type\x18\t \x01(\x0e\x32\x15.lnrpc.CommitmentType\x12\'\n\x1fremote_max_value_in_flight_msat\x18\n \x01(\x04\x12\x18\n\x10remote_max_htlcs\x18\x0b \x01(\r\x12\x15\n\rmax_local_csv\x18\x0c \x01(\r\x12\x11\n\tzero_conf\x18\r \x01(\x08\x12\x12\n\nscid_alias\x18\x0e \x01(\x08\x12\x10\n\x08\x62\x61se_fee\x18\x0f \x01(\x04\x12\x10\n\x08\x66\x65\x65_rate\x18\x10 \x01(\x04\x12\x14\n\x0cuse_base_fee\x18\x11 \x01(\x08\x12\x14\n\x0cuse_fee_rate\x18\x12 \x01(\x08\x12\x1f\n\x17remote_chan_reserve_sat\x18\x13 \x01(\x04\x12\x0c\n\x04memo\x18\x14 \x01(\t\"J\n\x18\x42\x61tchOpenChannelResponse\x12.\n\x10pending_channels\x18\x01 \x03(\x0b\x32\x14.lnrpc.PendingUpdate\"\xd6\x05\n\x12OpenChannelRequest\x12\x15\n\rsat_per_vbyte\x18\x01 \x01(\x04\x12\x13\n\x0bnode_pubkey\x18\x02 \x01(\x0c\x12\x1e\n\x12node_pubkey_string\x18\x03 \x01(\tB\x02\x18\x01\x12\x1c\n\x14local_funding_amount\x18\x04 \x01(\x03\x12\x10\n\x08push_sat\x18\x05 \x01(\x03\x12\x13\n\x0btarget_conf\x18\x06 \x01(\x05\x12\x18\n\x0csat_per_byte\x18\x07 \x01(\x03\x42\x02\x18\x01\x12\x0f\n\x07private\x18\x08 \x01(\x08\x12\x15\n\rmin_htlc_msat\x18\t \x01(\x03\x12\x18\n\x10remote_csv_delay\x18\n \x01(\r\x12\x11\n\tmin_confs\x18\x0b \x01(\x05\x12\x19\n\x11spend_unconfirmed\x18\x0c \x01(\x08\x12\x15\n\rclose_address\x18\r \x01(\t\x12(\n\x0c\x66unding_shim\x18\x0e \x01(\x0b\x32\x12.lnrpc.FundingShim\x12\'\n\x1fremote_max_value_in_flight_msat\x18\x0f \x01(\x04\x12\x18\n\x10remote_max_htlcs\x18\x10 \x01(\r\x12\x15\n\rmax_local_csv\x18\x11 \x01(\r\x12.\n\x0f\x63ommitment_type\x18\x12 \x01(\x0e\x32\x15.lnrpc.CommitmentType\x12\x11\n\tzero_conf\x18\x13 \x01(\x08\x12\x12\n\nscid_alias\x18\x14 \x01(\x08\x12\x10\n\x08\x62\x61se_fee\x18\x15 \x01(\x04\x12\x10\n\x08\x66\x65\x65_rate\x18\x16 \x01(\x04\x12\x14\n\x0cuse_base_fee\x18\x17 \x01(\x08\x12\x14\n\x0cuse_fee_rate\x18\x18 \x01(\x08\x12\x1f\n\x17remote_chan_reserve_sat\x18\x19 \x01(\x04\x12\x10\n\x08\x66und_max\x18\x1a \x01(\x08\x12\x0c\n\x04memo\x18\x1b \x01(\t\x12\"\n\toutpoints\x18\x1c \x03(\x0b\x32\x0f.lnrpc.OutPoint\"\xc3\x01\n\x10OpenStatusUpdate\x12,\n\x0c\x63han_pending\x18\x01 \x01(\x0b\x32\x14.lnrpc.PendingUpdateH\x00\x12-\n\tchan_open\x18\x03 \x01(\x0b\x32\x18.lnrpc.ChannelOpenUpdateH\x00\x12/\n\tpsbt_fund\x18\x05 \x01(\x0b\x32\x1a.lnrpc.ReadyForPsbtFundingH\x00\x12\x17\n\x0fpending_chan_id\x18\x04 \x01(\x0c\x42\x08\n\x06update\"3\n\nKeyLocator\x12\x12\n\nkey_family\x18\x01 \x01(\x05\x12\x11\n\tkey_index\x18\x02 \x01(\x05\"J\n\rKeyDescriptor\x12\x15\n\rraw_key_bytes\x18\x01 \x01(\x0c\x12\"\n\x07key_loc\x18\x02 \x01(\x0b\x32\x11.lnrpc.KeyLocator\"\xc0\x01\n\rChanPointShim\x12\x0b\n\x03\x61mt\x18\x01 \x01(\x03\x12\'\n\nchan_point\x18\x02 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\x12\'\n\tlocal_key\x18\x03 \x01(\x0b\x32\x14.lnrpc.KeyDescriptor\x12\x12\n\nremote_key\x18\x04 \x01(\x0c\x12\x17\n\x0fpending_chan_id\x18\x05 \x01(\x0c\x12\x13\n\x0bthaw_height\x18\x06 \x01(\r\x12\x0e\n\x06musig2\x18\x07 \x01(\x08\"J\n\x08PsbtShim\x12\x17\n\x0fpending_chan_id\x18\x01 \x01(\x0c\x12\x11\n\tbase_psbt\x18\x02 \x01(\x0c\x12\x12\n\nno_publish\x18\x03 \x01(\x08\"l\n\x0b\x46undingShim\x12/\n\x0f\x63han_point_shim\x18\x01 \x01(\x0b\x32\x14.lnrpc.ChanPointShimH\x00\x12$\n\tpsbt_shim\x18\x02 \x01(\x0b\x32\x0f.lnrpc.PsbtShimH\x00\x42\x06\n\x04shim\",\n\x11\x46undingShimCancel\x12\x17\n\x0fpending_chan_id\x18\x01 \x01(\x0c\"X\n\x11\x46undingPsbtVerify\x12\x13\n\x0b\x66unded_psbt\x18\x01 \x01(\x0c\x12\x17\n\x0fpending_chan_id\x18\x02 \x01(\x0c\x12\x15\n\rskip_finalize\x18\x03 \x01(\x08\"Y\n\x13\x46undingPsbtFinalize\x12\x13\n\x0bsigned_psbt\x18\x01 \x01(\x0c\x12\x17\n\x0fpending_chan_id\x18\x02 \x01(\x0c\x12\x14\n\x0c\x66inal_raw_tx\x18\x03 \x01(\x0c\"\xe5\x01\n\x14\x46undingTransitionMsg\x12+\n\rshim_register\x18\x01 \x01(\x0b\x32\x12.lnrpc.FundingShimH\x00\x12/\n\x0bshim_cancel\x18\x02 \x01(\x0b\x32\x18.lnrpc.FundingShimCancelH\x00\x12/\n\x0bpsbt_verify\x18\x03 \x01(\x0b\x32\x18.lnrpc.FundingPsbtVerifyH\x00\x12\x33\n\rpsbt_finalize\x18\x04 \x01(\x0b\x32\x1a.lnrpc.FundingPsbtFinalizeH\x00\x42\t\n\x07trigger\"\x16\n\x14\x46undingStateStepResp\"\x86\x01\n\x0bPendingHTLC\x12\x10\n\x08incoming\x18\x01 \x01(\x08\x12\x0e\n\x06\x61mount\x18\x02 \x01(\x03\x12\x10\n\x08outpoint\x18\x03 \x01(\t\x12\x17\n\x0fmaturity_height\x18\x04 \x01(\r\x12\x1b\n\x13\x62locks_til_maturity\x18\x05 \x01(\x05\x12\r\n\x05stage\x18\x06 \x01(\r\"\x18\n\x16PendingChannelsRequest\"\xa4\x0e\n\x17PendingChannelsResponse\x12\x1b\n\x13total_limbo_balance\x18\x01 \x01(\x03\x12P\n\x15pending_open_channels\x18\x02 \x03(\x0b\x32\x31.lnrpc.PendingChannelsResponse.PendingOpenChannel\x12R\n\x18pending_closing_channels\x18\x03 \x03(\x0b\x32,.lnrpc.PendingChannelsResponse.ClosedChannelB\x02\x18\x01\x12Y\n\x1epending_force_closing_channels\x18\x04 \x03(\x0b\x32\x31.lnrpc.PendingChannelsResponse.ForceClosedChannel\x12R\n\x16waiting_close_channels\x18\x05 \x03(\x0b\x32\x32.lnrpc.PendingChannelsResponse.WaitingCloseChannel\x1a\xf2\x02\n\x0ePendingChannel\x12\x17\n\x0fremote_node_pub\x18\x01 \x01(\t\x12\x15\n\rchannel_point\x18\x02 \x01(\t\x12\x10\n\x08\x63\x61pacity\x18\x03 \x01(\x03\x12\x15\n\rlocal_balance\x18\x04 \x01(\x03\x12\x16\n\x0eremote_balance\x18\x05 \x01(\x03\x12\x1e\n\x16local_chan_reserve_sat\x18\x06 \x01(\x03\x12\x1f\n\x17remote_chan_reserve_sat\x18\x07 \x01(\x03\x12#\n\tinitiator\x18\x08 \x01(\x0e\x32\x10.lnrpc.Initiator\x12.\n\x0f\x63ommitment_type\x18\t \x01(\x0e\x32\x15.lnrpc.CommitmentType\x12\x1f\n\x17num_forwarding_packages\x18\n \x01(\x03\x12\x19\n\x11\x63han_status_flags\x18\x0b \x01(\t\x12\x0f\n\x07private\x18\x0c \x01(\x08\x12\x0c\n\x04memo\x18\r \x01(\t\x1a\xb8\x01\n\x12PendingOpenChannel\x12>\n\x07\x63hannel\x18\x01 \x01(\x0b\x32-.lnrpc.PendingChannelsResponse.PendingChannel\x12\x12\n\ncommit_fee\x18\x04 \x01(\x03\x12\x15\n\rcommit_weight\x18\x05 \x01(\x03\x12\x12\n\nfee_per_kw\x18\x06 \x01(\x03\x12\x1d\n\x15\x66unding_expiry_blocks\x18\x03 \x01(\x05J\x04\x08\x02\x10\x03\x1a\xc3\x01\n\x13WaitingCloseChannel\x12>\n\x07\x63hannel\x18\x01 \x01(\x0b\x32-.lnrpc.PendingChannelsResponse.PendingChannel\x12\x15\n\rlimbo_balance\x18\x02 \x01(\x03\x12?\n\x0b\x63ommitments\x18\x03 \x01(\x0b\x32*.lnrpc.PendingChannelsResponse.Commitments\x12\x14\n\x0c\x63losing_txid\x18\x04 \x01(\t\x1a\xb7\x01\n\x0b\x43ommitments\x12\x12\n\nlocal_txid\x18\x01 \x01(\t\x12\x13\n\x0bremote_txid\x18\x02 \x01(\t\x12\x1b\n\x13remote_pending_txid\x18\x03 \x01(\t\x12\x1c\n\x14local_commit_fee_sat\x18\x04 \x01(\x04\x12\x1d\n\x15remote_commit_fee_sat\x18\x05 \x01(\x04\x12%\n\x1dremote_pending_commit_fee_sat\x18\x06 \x01(\x04\x1a\x65\n\rClosedChannel\x12>\n\x07\x63hannel\x18\x01 \x01(\x0b\x32-.lnrpc.PendingChannelsResponse.PendingChannel\x12\x14\n\x0c\x63losing_txid\x18\x02 \x01(\t\x1a\xff\x02\n\x12\x46orceClosedChannel\x12>\n\x07\x63hannel\x18\x01 \x01(\x0b\x32-.lnrpc.PendingChannelsResponse.PendingChannel\x12\x14\n\x0c\x63losing_txid\x18\x02 \x01(\t\x12\x15\n\rlimbo_balance\x18\x03 \x01(\x03\x12\x17\n\x0fmaturity_height\x18\x04 \x01(\r\x12\x1b\n\x13\x62locks_til_maturity\x18\x05 \x01(\x05\x12\x19\n\x11recovered_balance\x18\x06 \x01(\x03\x12)\n\rpending_htlcs\x18\x08 \x03(\x0b\x32\x12.lnrpc.PendingHTLC\x12M\n\x06\x61nchor\x18\t \x01(\x0e\x32=.lnrpc.PendingChannelsResponse.ForceClosedChannel.AnchorState\"1\n\x0b\x41nchorState\x12\t\n\x05LIMBO\x10\x00\x12\r\n\tRECOVERED\x10\x01\x12\x08\n\x04LOST\x10\x02\"\x1a\n\x18\x43hannelEventSubscription\"\x93\x04\n\x12\x43hannelEventUpdate\x12&\n\x0copen_channel\x18\x01 \x01(\x0b\x32\x0e.lnrpc.ChannelH\x00\x12\x34\n\x0e\x63losed_channel\x18\x02 \x01(\x0b\x32\x1a.lnrpc.ChannelCloseSummaryH\x00\x12-\n\x0e\x61\x63tive_channel\x18\x03 \x01(\x0b\x32\x13.lnrpc.ChannelPointH\x00\x12/\n\x10inactive_channel\x18\x04 \x01(\x0b\x32\x13.lnrpc.ChannelPointH\x00\x12\x34\n\x14pending_open_channel\x18\x06 \x01(\x0b\x32\x14.lnrpc.PendingUpdateH\x00\x12\x35\n\x16\x66ully_resolved_channel\x18\x07 \x01(\x0b\x32\x13.lnrpc.ChannelPointH\x00\x12\x32\n\x04type\x18\x05 \x01(\x0e\x32$.lnrpc.ChannelEventUpdate.UpdateType\"\x92\x01\n\nUpdateType\x12\x10\n\x0cOPEN_CHANNEL\x10\x00\x12\x12\n\x0e\x43LOSED_CHANNEL\x10\x01\x12\x12\n\x0e\x41\x43TIVE_CHANNEL\x10\x02\x12\x14\n\x10INACTIVE_CHANNEL\x10\x03\x12\x18\n\x14PENDING_OPEN_CHANNEL\x10\x04\x12\x1a\n\x16\x46ULLY_RESOLVED_CHANNEL\x10\x05\x42\t\n\x07\x63hannel\"N\n\x14WalletAccountBalance\x12\x19\n\x11\x63onfirmed_balance\x18\x01 \x01(\x03\x12\x1b\n\x13unconfirmed_balance\x18\x02 \x01(\x03\"\'\n\x14WalletBalanceRequest\x12\x0f\n\x07\x61\x63\x63ount\x18\x01 \x01(\t\"\xc3\x02\n\x15WalletBalanceResponse\x12\x15\n\rtotal_balance\x18\x01 \x01(\x03\x12\x19\n\x11\x63onfirmed_balance\x18\x02 \x01(\x03\x12\x1b\n\x13unconfirmed_balance\x18\x03 \x01(\x03\x12\x16\n\x0elocked_balance\x18\x05 \x01(\x03\x12$\n\x1creserved_balance_anchor_chan\x18\x06 \x01(\x03\x12I\n\x0f\x61\x63\x63ount_balance\x18\x04 \x03(\x0b\x32\x30.lnrpc.WalletBalanceResponse.AccountBalanceEntry\x1aR\n\x13\x41\x63\x63ountBalanceEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.lnrpc.WalletAccountBalance:\x02\x38\x01\"#\n\x06\x41mount\x12\x0b\n\x03sat\x18\x01 \x01(\x04\x12\x0c\n\x04msat\x18\x02 \x01(\x04\"\x17\n\x15\x43hannelBalanceRequest\"\xe4\x02\n\x16\x43hannelBalanceResponse\x12\x13\n\x07\x62\x61lance\x18\x01 \x01(\x03\x42\x02\x18\x01\x12 \n\x14pending_open_balance\x18\x02 \x01(\x03\x42\x02\x18\x01\x12$\n\rlocal_balance\x18\x03 \x01(\x0b\x32\r.lnrpc.Amount\x12%\n\x0eremote_balance\x18\x04 \x01(\x0b\x32\r.lnrpc.Amount\x12.\n\x17unsettled_local_balance\x18\x05 \x01(\x0b\x32\r.lnrpc.Amount\x12/\n\x18unsettled_remote_balance\x18\x06 \x01(\x0b\x32\r.lnrpc.Amount\x12\x31\n\x1apending_open_local_balance\x18\x07 \x01(\x0b\x32\r.lnrpc.Amount\x12\x32\n\x1bpending_open_remote_balance\x18\x08 \x01(\x0b\x32\r.lnrpc.Amount\"\x9d\x05\n\x12QueryRoutesRequest\x12\x0f\n\x07pub_key\x18\x01 \x01(\t\x12\x0b\n\x03\x61mt\x18\x02 \x01(\x03\x12\x10\n\x08\x61mt_msat\x18\x0c \x01(\x03\x12\x18\n\x10\x66inal_cltv_delta\x18\x04 \x01(\x05\x12\"\n\tfee_limit\x18\x05 \x01(\x0b\x32\x0f.lnrpc.FeeLimit\x12\x15\n\rignored_nodes\x18\x06 \x03(\x0c\x12-\n\rignored_edges\x18\x07 \x03(\x0b\x32\x12.lnrpc.EdgeLocatorB\x02\x18\x01\x12\x16\n\x0esource_pub_key\x18\x08 \x01(\t\x12\x1b\n\x13use_mission_control\x18\t \x01(\x08\x12&\n\rignored_pairs\x18\n \x03(\x0b\x32\x0f.lnrpc.NodePair\x12\x12\n\ncltv_limit\x18\x0b \x01(\r\x12M\n\x13\x64\x65st_custom_records\x18\r \x03(\x0b\x32\x30.lnrpc.QueryRoutesRequest.DestCustomRecordsEntry\x12\x1c\n\x10outgoing_chan_id\x18\x0e \x01(\x04\x42\x02\x30\x01\x12\x17\n\x0flast_hop_pubkey\x18\x0f \x01(\x0c\x12%\n\x0broute_hints\x18\x10 \x03(\x0b\x32\x10.lnrpc.RouteHint\x12\x38\n\x15\x62linded_payment_paths\x18\x13 \x03(\x0b\x32\x19.lnrpc.BlindedPaymentPath\x12(\n\rdest_features\x18\x11 \x03(\x0e\x32\x11.lnrpc.FeatureBit\x12\x11\n\ttime_pref\x18\x12 \x01(\x01\x1a\x38\n\x16\x44\x65stCustomRecordsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01J\x04\x08\x03\x10\x04\"$\n\x08NodePair\x12\x0c\n\x04\x66rom\x18\x01 \x01(\x0c\x12\n\n\x02to\x18\x02 \x01(\x0c\"@\n\x0b\x45\x64geLocator\x12\x16\n\nchannel_id\x18\x01 \x01(\x04\x42\x02\x30\x01\x12\x19\n\x11\x64irection_reverse\x18\x02 \x01(\x08\"I\n\x13QueryRoutesResponse\x12\x1c\n\x06routes\x18\x01 \x03(\x0b\x32\x0c.lnrpc.Route\x12\x14\n\x0csuccess_prob\x18\x02 \x01(\x01\"\xde\x03\n\x03Hop\x12\x13\n\x07\x63han_id\x18\x01 \x01(\x04\x42\x02\x30\x01\x12\x19\n\rchan_capacity\x18\x02 \x01(\x03\x42\x02\x18\x01\x12\x1a\n\x0e\x61mt_to_forward\x18\x03 \x01(\x03\x42\x02\x18\x01\x12\x0f\n\x03\x66\x65\x65\x18\x04 \x01(\x03\x42\x02\x18\x01\x12\x0e\n\x06\x65xpiry\x18\x05 \x01(\r\x12\x1b\n\x13\x61mt_to_forward_msat\x18\x06 \x01(\x03\x12\x10\n\x08\x66\x65\x65_msat\x18\x07 \x01(\x03\x12\x0f\n\x07pub_key\x18\x08 \x01(\t\x12\x17\n\x0btlv_payload\x18\t \x01(\x08\x42\x02\x18\x01\x12$\n\nmpp_record\x18\n \x01(\x0b\x32\x10.lnrpc.MPPRecord\x12$\n\namp_record\x18\x0c \x01(\x0b\x32\x10.lnrpc.AMPRecord\x12\x35\n\x0e\x63ustom_records\x18\x0b \x03(\x0b\x32\x1d.lnrpc.Hop.CustomRecordsEntry\x12\x10\n\x08metadata\x18\r \x01(\x0c\x12\x16\n\x0e\x62linding_point\x18\x0e \x01(\x0c\x12\x16\n\x0e\x65ncrypted_data\x18\x0f \x01(\x0c\x12\x16\n\x0etotal_amt_msat\x18\x10 \x01(\x04\x1a\x34\n\x12\x43ustomRecordsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\"9\n\tMPPRecord\x12\x14\n\x0cpayment_addr\x18\x0b \x01(\x0c\x12\x16\n\x0etotal_amt_msat\x18\n \x01(\x03\"D\n\tAMPRecord\x12\x12\n\nroot_share\x18\x01 \x01(\x0c\x12\x0e\n\x06set_id\x18\x02 \x01(\x0c\x12\x13\n\x0b\x63hild_index\x18\x03 \x01(\r\"\x9a\x01\n\x05Route\x12\x17\n\x0ftotal_time_lock\x18\x01 \x01(\r\x12\x16\n\ntotal_fees\x18\x02 \x01(\x03\x42\x02\x18\x01\x12\x15\n\ttotal_amt\x18\x03 \x01(\x03\x42\x02\x18\x01\x12\x18\n\x04hops\x18\x04 \x03(\x0b\x32\n.lnrpc.Hop\x12\x17\n\x0ftotal_fees_msat\x18\x05 \x01(\x03\x12\x16\n\x0etotal_amt_msat\x18\x06 \x01(\x03\"<\n\x0fNodeInfoRequest\x12\x0f\n\x07pub_key\x18\x01 \x01(\t\x12\x18\n\x10include_channels\x18\x02 \x01(\x08\"\x82\x01\n\x08NodeInfo\x12\"\n\x04node\x18\x01 \x01(\x0b\x32\x14.lnrpc.LightningNode\x12\x14\n\x0cnum_channels\x18\x02 \x01(\r\x12\x16\n\x0etotal_capacity\x18\x03 \x01(\x03\x12$\n\x08\x63hannels\x18\x04 \x03(\x0b\x32\x12.lnrpc.ChannelEdge\"\xe8\x02\n\rLightningNode\x12\x13\n\x0blast_update\x18\x01 \x01(\r\x12\x0f\n\x07pub_key\x18\x02 \x01(\t\x12\r\n\x05\x61lias\x18\x03 \x01(\t\x12%\n\taddresses\x18\x04 \x03(\x0b\x32\x12.lnrpc.NodeAddress\x12\r\n\x05\x63olor\x18\x05 \x01(\t\x12\x34\n\x08\x66\x65\x61tures\x18\x06 \x03(\x0b\x32\".lnrpc.LightningNode.FeaturesEntry\x12?\n\x0e\x63ustom_records\x18\x07 \x03(\x0b\x32\'.lnrpc.LightningNode.CustomRecordsEntry\x1a?\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x0e.lnrpc.Feature:\x02\x38\x01\x1a\x34\n\x12\x43ustomRecordsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\",\n\x0bNodeAddress\x12\x0f\n\x07network\x18\x01 \x01(\t\x12\x0c\n\x04\x61\x64\x64r\x18\x02 \x01(\t\"\xa3\x02\n\rRoutingPolicy\x12\x17\n\x0ftime_lock_delta\x18\x01 \x01(\r\x12\x10\n\x08min_htlc\x18\x02 \x01(\x03\x12\x15\n\rfee_base_msat\x18\x03 \x01(\x03\x12\x1b\n\x13\x66\x65\x65_rate_milli_msat\x18\x04 \x01(\x03\x12\x10\n\x08\x64isabled\x18\x05 \x01(\x08\x12\x15\n\rmax_htlc_msat\x18\x06 \x01(\x04\x12\x13\n\x0blast_update\x18\x07 \x01(\r\x12?\n\x0e\x63ustom_records\x18\x08 \x03(\x0b\x32\'.lnrpc.RoutingPolicy.CustomRecordsEntry\x1a\x34\n\x12\x43ustomRecordsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\"\xd7\x02\n\x0b\x43hannelEdge\x12\x16\n\nchannel_id\x18\x01 \x01(\x04\x42\x02\x30\x01\x12\x12\n\nchan_point\x18\x02 \x01(\t\x12\x17\n\x0blast_update\x18\x03 \x01(\rB\x02\x18\x01\x12\x11\n\tnode1_pub\x18\x04 \x01(\t\x12\x11\n\tnode2_pub\x18\x05 \x01(\t\x12\x10\n\x08\x63\x61pacity\x18\x06 \x01(\x03\x12*\n\x0cnode1_policy\x18\x07 \x01(\x0b\x32\x14.lnrpc.RoutingPolicy\x12*\n\x0cnode2_policy\x18\x08 \x01(\x0b\x32\x14.lnrpc.RoutingPolicy\x12=\n\x0e\x63ustom_records\x18\t \x03(\x0b\x32%.lnrpc.ChannelEdge.CustomRecordsEntry\x1a\x34\n\x12\x43ustomRecordsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\"2\n\x13\x43hannelGraphRequest\x12\x1b\n\x13include_unannounced\x18\x01 \x01(\x08\"V\n\x0c\x43hannelGraph\x12#\n\x05nodes\x18\x01 \x03(\x0b\x32\x14.lnrpc.LightningNode\x12!\n\x05\x65\x64ges\x18\x02 \x03(\x0b\x32\x12.lnrpc.ChannelEdge\":\n\x12NodeMetricsRequest\x12$\n\x05types\x18\x01 \x03(\x0e\x32\x15.lnrpc.NodeMetricType\"\xbe\x01\n\x13NodeMetricsResponse\x12U\n\x16\x62\x65tweenness_centrality\x18\x01 \x03(\x0b\x32\x35.lnrpc.NodeMetricsResponse.BetweennessCentralityEntry\x1aP\n\x1a\x42\x65tweennessCentralityEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12!\n\x05value\x18\x02 \x01(\x0b\x32\x12.lnrpc.FloatMetric:\x02\x38\x01\"6\n\x0b\x46loatMetric\x12\r\n\x05value\x18\x01 \x01(\x01\x12\x18\n\x10normalized_value\x18\x02 \x01(\x01\"&\n\x0f\x43hanInfoRequest\x12\x13\n\x07\x63han_id\x18\x01 \x01(\x04\x42\x02\x30\x01\"\x14\n\x12NetworkInfoRequest\"\xa7\x02\n\x0bNetworkInfo\x12\x16\n\x0egraph_diameter\x18\x01 \x01(\r\x12\x16\n\x0e\x61vg_out_degree\x18\x02 \x01(\x01\x12\x16\n\x0emax_out_degree\x18\x03 \x01(\r\x12\x11\n\tnum_nodes\x18\x04 \x01(\r\x12\x14\n\x0cnum_channels\x18\x05 \x01(\r\x12\x1e\n\x16total_network_capacity\x18\x06 \x01(\x03\x12\x18\n\x10\x61vg_channel_size\x18\x07 \x01(\x01\x12\x18\n\x10min_channel_size\x18\x08 \x01(\x03\x12\x18\n\x10max_channel_size\x18\t \x01(\x03\x12\x1f\n\x17median_channel_size_sat\x18\n \x01(\x03\x12\x18\n\x10num_zombie_chans\x18\x0b \x01(\x04\"\r\n\x0bStopRequest\"\x0e\n\x0cStopResponse\"\x1b\n\x19GraphTopologySubscription\"\xa3\x01\n\x13GraphTopologyUpdate\x12\'\n\x0cnode_updates\x18\x01 \x03(\x0b\x32\x11.lnrpc.NodeUpdate\x12\x31\n\x0f\x63hannel_updates\x18\x02 \x03(\x0b\x32\x18.lnrpc.ChannelEdgeUpdate\x12\x30\n\x0c\x63losed_chans\x18\x03 \x03(\x0b\x32\x1a.lnrpc.ClosedChannelUpdate\"\x94\x02\n\nNodeUpdate\x12\x15\n\taddresses\x18\x01 \x03(\tB\x02\x18\x01\x12\x14\n\x0cidentity_key\x18\x02 \x01(\t\x12\x1b\n\x0fglobal_features\x18\x03 \x01(\x0c\x42\x02\x18\x01\x12\r\n\x05\x61lias\x18\x04 \x01(\t\x12\r\n\x05\x63olor\x18\x05 \x01(\t\x12*\n\x0enode_addresses\x18\x07 \x03(\x0b\x32\x12.lnrpc.NodeAddress\x12\x31\n\x08\x66\x65\x61tures\x18\x06 \x03(\x0b\x32\x1f.lnrpc.NodeUpdate.FeaturesEntry\x1a?\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x0e.lnrpc.Feature:\x02\x38\x01\"\xc4\x01\n\x11\x43hannelEdgeUpdate\x12\x13\n\x07\x63han_id\x18\x01 \x01(\x04\x42\x02\x30\x01\x12\'\n\nchan_point\x18\x02 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\x12\x10\n\x08\x63\x61pacity\x18\x03 \x01(\x03\x12,\n\x0erouting_policy\x18\x04 \x01(\x0b\x32\x14.lnrpc.RoutingPolicy\x12\x18\n\x10\x61\x64vertising_node\x18\x05 \x01(\t\x12\x17\n\x0f\x63onnecting_node\x18\x06 \x01(\t\"|\n\x13\x43losedChannelUpdate\x12\x13\n\x07\x63han_id\x18\x01 \x01(\x04\x42\x02\x30\x01\x12\x10\n\x08\x63\x61pacity\x18\x02 \x01(\x03\x12\x15\n\rclosed_height\x18\x03 \x01(\r\x12\'\n\nchan_point\x18\x04 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\"\x86\x01\n\x07HopHint\x12\x0f\n\x07node_id\x18\x01 \x01(\t\x12\x13\n\x07\x63han_id\x18\x02 \x01(\x04\x42\x02\x30\x01\x12\x15\n\rfee_base_msat\x18\x03 \x01(\r\x12#\n\x1b\x66\x65\x65_proportional_millionths\x18\x04 \x01(\r\x12\x19\n\x11\x63ltv_expiry_delta\x18\x05 \x01(\r\"\x17\n\x05SetID\x12\x0e\n\x06set_id\x18\x01 \x01(\x0c\".\n\tRouteHint\x12!\n\thop_hints\x18\x01 \x03(\x0b\x32\x0e.lnrpc.HopHint\"\xe1\x01\n\x12\x42lindedPaymentPath\x12(\n\x0c\x62linded_path\x18\x01 \x01(\x0b\x32\x12.lnrpc.BlindedPath\x12\x15\n\rbase_fee_msat\x18\x02 \x01(\x04\x12\x1d\n\x15proportional_fee_msat\x18\x03 \x01(\x04\x12\x18\n\x10total_cltv_delta\x18\x04 \x01(\r\x12\x15\n\rhtlc_min_msat\x18\x05 \x01(\x04\x12\x15\n\rhtlc_max_msat\x18\x06 \x01(\x04\x12#\n\x08\x66\x65\x61tures\x18\x07 \x03(\x0e\x32\x11.lnrpc.FeatureBit\"i\n\x0b\x42lindedPath\x12\x19\n\x11introduction_node\x18\x01 \x01(\x0c\x12\x16\n\x0e\x62linding_point\x18\x02 \x01(\x0c\x12\'\n\x0c\x62linded_hops\x18\x03 \x03(\x0b\x32\x11.lnrpc.BlindedHop\":\n\nBlindedHop\x12\x14\n\x0c\x62linded_node\x18\x01 \x01(\x0c\x12\x16\n\x0e\x65ncrypted_data\x18\x02 \x01(\x0c\"{\n\x0f\x41MPInvoiceState\x12&\n\x05state\x18\x01 \x01(\x0e\x32\x17.lnrpc.InvoiceHTLCState\x12\x14\n\x0csettle_index\x18\x02 \x01(\x04\x12\x13\n\x0bsettle_time\x18\x03 \x01(\x03\x12\x15\n\ramt_paid_msat\x18\x05 \x01(\x03\"\x85\x07\n\x07Invoice\x12\x0c\n\x04memo\x18\x01 \x01(\t\x12\x12\n\nr_preimage\x18\x03 \x01(\x0c\x12\x0e\n\x06r_hash\x18\x04 \x01(\x0c\x12\r\n\x05value\x18\x05 \x01(\x03\x12\x12\n\nvalue_msat\x18\x17 \x01(\x03\x12\x13\n\x07settled\x18\x06 \x01(\x08\x42\x02\x18\x01\x12\x15\n\rcreation_date\x18\x07 \x01(\x03\x12\x13\n\x0bsettle_date\x18\x08 \x01(\x03\x12\x17\n\x0fpayment_request\x18\t \x01(\t\x12\x18\n\x10\x64\x65scription_hash\x18\n \x01(\x0c\x12\x0e\n\x06\x65xpiry\x18\x0b \x01(\x03\x12\x15\n\rfallback_addr\x18\x0c \x01(\t\x12\x13\n\x0b\x63ltv_expiry\x18\r \x01(\x04\x12%\n\x0broute_hints\x18\x0e \x03(\x0b\x32\x10.lnrpc.RouteHint\x12\x0f\n\x07private\x18\x0f \x01(\x08\x12\x11\n\tadd_index\x18\x10 \x01(\x04\x12\x14\n\x0csettle_index\x18\x11 \x01(\x04\x12\x14\n\x08\x61mt_paid\x18\x12 \x01(\x03\x42\x02\x18\x01\x12\x14\n\x0c\x61mt_paid_sat\x18\x13 \x01(\x03\x12\x15\n\ramt_paid_msat\x18\x14 \x01(\x03\x12*\n\x05state\x18\x15 \x01(\x0e\x32\x1b.lnrpc.Invoice.InvoiceState\x12!\n\x05htlcs\x18\x16 \x03(\x0b\x32\x12.lnrpc.InvoiceHTLC\x12.\n\x08\x66\x65\x61tures\x18\x18 \x03(\x0b\x32\x1c.lnrpc.Invoice.FeaturesEntry\x12\x12\n\nis_keysend\x18\x19 \x01(\x08\x12\x14\n\x0cpayment_addr\x18\x1a \x01(\x0c\x12\x0e\n\x06is_amp\x18\x1b \x01(\x08\x12>\n\x11\x61mp_invoice_state\x18\x1c \x03(\x0b\x32#.lnrpc.Invoice.AmpInvoiceStateEntry\x1a?\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x0e.lnrpc.Feature:\x02\x38\x01\x1aN\n\x14\x41mpInvoiceStateEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.lnrpc.AMPInvoiceState:\x02\x38\x01\"A\n\x0cInvoiceState\x12\x08\n\x04OPEN\x10\x00\x12\x0b\n\x07SETTLED\x10\x01\x12\x0c\n\x08\x43\x41NCELED\x10\x02\x12\x0c\n\x08\x41\x43\x43\x45PTED\x10\x03J\x04\x08\x02\x10\x03\"\xf3\x02\n\x0bInvoiceHTLC\x12\x13\n\x07\x63han_id\x18\x01 \x01(\x04\x42\x02\x30\x01\x12\x12\n\nhtlc_index\x18\x02 \x01(\x04\x12\x10\n\x08\x61mt_msat\x18\x03 \x01(\x04\x12\x15\n\raccept_height\x18\x04 \x01(\x05\x12\x13\n\x0b\x61\x63\x63\x65pt_time\x18\x05 \x01(\x03\x12\x14\n\x0cresolve_time\x18\x06 \x01(\x03\x12\x15\n\rexpiry_height\x18\x07 \x01(\x05\x12&\n\x05state\x18\x08 \x01(\x0e\x32\x17.lnrpc.InvoiceHTLCState\x12=\n\x0e\x63ustom_records\x18\t \x03(\x0b\x32%.lnrpc.InvoiceHTLC.CustomRecordsEntry\x12\x1a\n\x12mpp_total_amt_msat\x18\n \x01(\x04\x12\x17\n\x03\x61mp\x18\x0b \x01(\x0b\x32\n.lnrpc.AMP\x1a\x34\n\x12\x43ustomRecordsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\"^\n\x03\x41MP\x12\x12\n\nroot_share\x18\x01 \x01(\x0c\x12\x0e\n\x06set_id\x18\x02 \x01(\x0c\x12\x13\n\x0b\x63hild_index\x18\x03 \x01(\r\x12\x0c\n\x04hash\x18\x04 \x01(\x0c\x12\x10\n\x08preimage\x18\x05 \x01(\x0c\"f\n\x12\x41\x64\x64InvoiceResponse\x12\x0e\n\x06r_hash\x18\x01 \x01(\x0c\x12\x17\n\x0fpayment_request\x18\x02 \x01(\t\x12\x11\n\tadd_index\x18\x10 \x01(\x04\x12\x14\n\x0cpayment_addr\x18\x11 \x01(\x0c\"5\n\x0bPaymentHash\x12\x16\n\nr_hash_str\x18\x01 \x01(\tB\x02\x18\x01\x12\x0e\n\x06r_hash\x18\x02 \x01(\x0c\"\xa4\x01\n\x12ListInvoiceRequest\x12\x14\n\x0cpending_only\x18\x01 \x01(\x08\x12\x14\n\x0cindex_offset\x18\x04 \x01(\x04\x12\x18\n\x10num_max_invoices\x18\x05 \x01(\x04\x12\x10\n\x08reversed\x18\x06 \x01(\x08\x12\x1b\n\x13\x63reation_date_start\x18\x07 \x01(\x04\x12\x19\n\x11\x63reation_date_end\x18\x08 \x01(\x04\"n\n\x13ListInvoiceResponse\x12 \n\x08invoices\x18\x01 \x03(\x0b\x32\x0e.lnrpc.Invoice\x12\x19\n\x11last_index_offset\x18\x02 \x01(\x04\x12\x1a\n\x12\x66irst_index_offset\x18\x03 \x01(\x04\">\n\x13InvoiceSubscription\x12\x11\n\tadd_index\x18\x01 \x01(\x04\x12\x14\n\x0csettle_index\x18\x02 \x01(\x04\"\xf3\x03\n\x07Payment\x12\x14\n\x0cpayment_hash\x18\x01 \x01(\t\x12\x11\n\x05value\x18\x02 \x01(\x03\x42\x02\x18\x01\x12\x19\n\rcreation_date\x18\x03 \x01(\x03\x42\x02\x18\x01\x12\x0f\n\x03\x66\x65\x65\x18\x05 \x01(\x03\x42\x02\x18\x01\x12\x18\n\x10payment_preimage\x18\x06 \x01(\t\x12\x11\n\tvalue_sat\x18\x07 \x01(\x03\x12\x12\n\nvalue_msat\x18\x08 \x01(\x03\x12\x17\n\x0fpayment_request\x18\t \x01(\t\x12,\n\x06status\x18\n \x01(\x0e\x32\x1c.lnrpc.Payment.PaymentStatus\x12\x0f\n\x07\x66\x65\x65_sat\x18\x0b \x01(\x03\x12\x10\n\x08\x66\x65\x65_msat\x18\x0c \x01(\x03\x12\x18\n\x10\x63reation_time_ns\x18\r \x01(\x03\x12!\n\x05htlcs\x18\x0e \x03(\x0b\x32\x12.lnrpc.HTLCAttempt\x12\x15\n\rpayment_index\x18\x0f \x01(\x04\x12\x33\n\x0e\x66\x61ilure_reason\x18\x10 \x01(\x0e\x32\x1b.lnrpc.PaymentFailureReason\"Y\n\rPaymentStatus\x12\x0f\n\x07UNKNOWN\x10\x00\x1a\x02\x08\x01\x12\r\n\tIN_FLIGHT\x10\x01\x12\r\n\tSUCCEEDED\x10\x02\x12\n\n\x06\x46\x41ILED\x10\x03\x12\r\n\tINITIATED\x10\x04J\x04\x08\x04\x10\x05\"\x8a\x02\n\x0bHTLCAttempt\x12\x12\n\nattempt_id\x18\x07 \x01(\x04\x12-\n\x06status\x18\x01 \x01(\x0e\x32\x1d.lnrpc.HTLCAttempt.HTLCStatus\x12\x1b\n\x05route\x18\x02 \x01(\x0b\x32\x0c.lnrpc.Route\x12\x17\n\x0f\x61ttempt_time_ns\x18\x03 \x01(\x03\x12\x17\n\x0fresolve_time_ns\x18\x04 \x01(\x03\x12\x1f\n\x07\x66\x61ilure\x18\x05 \x01(\x0b\x32\x0e.lnrpc.Failure\x12\x10\n\x08preimage\x18\x06 \x01(\x0c\"6\n\nHTLCStatus\x12\r\n\tIN_FLIGHT\x10\x00\x12\r\n\tSUCCEEDED\x10\x01\x12\n\n\x06\x46\x41ILED\x10\x02\"\xc5\x01\n\x13ListPaymentsRequest\x12\x1a\n\x12include_incomplete\x18\x01 \x01(\x08\x12\x14\n\x0cindex_offset\x18\x02 \x01(\x04\x12\x14\n\x0cmax_payments\x18\x03 \x01(\x04\x12\x10\n\x08reversed\x18\x04 \x01(\x08\x12\x1c\n\x14\x63ount_total_payments\x18\x05 \x01(\x08\x12\x1b\n\x13\x63reation_date_start\x18\x06 \x01(\x04\x12\x19\n\x11\x63reation_date_end\x18\x07 \x01(\x04\"\x8b\x01\n\x14ListPaymentsResponse\x12 \n\x08payments\x18\x01 \x03(\x0b\x32\x0e.lnrpc.Payment\x12\x1a\n\x12\x66irst_index_offset\x18\x02 \x01(\x04\x12\x19\n\x11last_index_offset\x18\x03 \x01(\x04\x12\x1a\n\x12total_num_payments\x18\x04 \x01(\x04\"G\n\x14\x44\x65letePaymentRequest\x12\x14\n\x0cpayment_hash\x18\x01 \x01(\x0c\x12\x19\n\x11\x66\x61iled_htlcs_only\x18\x02 \x01(\x08\"S\n\x18\x44\x65leteAllPaymentsRequest\x12\x1c\n\x14\x66\x61iled_payments_only\x18\x01 \x01(\x08\x12\x19\n\x11\x66\x61iled_htlcs_only\x18\x02 \x01(\x08\"\x17\n\x15\x44\x65letePaymentResponse\"\x1b\n\x19\x44\x65leteAllPaymentsResponse\"\x86\x01\n\x15\x41\x62\x61ndonChannelRequest\x12*\n\rchannel_point\x18\x01 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\x12!\n\x19pending_funding_shim_only\x18\x02 \x01(\x08\x12\x1e\n\x16i_know_what_i_am_doing\x18\x03 \x01(\x08\"\x18\n\x16\x41\x62\x61ndonChannelResponse\"5\n\x11\x44\x65\x62ugLevelRequest\x12\x0c\n\x04show\x18\x01 \x01(\x08\x12\x12\n\nlevel_spec\x18\x02 \x01(\t\")\n\x12\x44\x65\x62ugLevelResponse\x12\x13\n\x0bsub_systems\x18\x01 \x01(\t\"\x1f\n\x0cPayReqString\x12\x0f\n\x07pay_req\x18\x01 \x01(\t\"\x86\x03\n\x06PayReq\x12\x13\n\x0b\x64\x65stination\x18\x01 \x01(\t\x12\x14\n\x0cpayment_hash\x18\x02 \x01(\t\x12\x14\n\x0cnum_satoshis\x18\x03 \x01(\x03\x12\x11\n\ttimestamp\x18\x04 \x01(\x03\x12\x0e\n\x06\x65xpiry\x18\x05 \x01(\x03\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x18\n\x10\x64\x65scription_hash\x18\x07 \x01(\t\x12\x15\n\rfallback_addr\x18\x08 \x01(\t\x12\x13\n\x0b\x63ltv_expiry\x18\t \x01(\x03\x12%\n\x0broute_hints\x18\n \x03(\x0b\x32\x10.lnrpc.RouteHint\x12\x14\n\x0cpayment_addr\x18\x0b \x01(\x0c\x12\x10\n\x08num_msat\x18\x0c \x01(\x03\x12-\n\x08\x66\x65\x61tures\x18\r \x03(\x0b\x32\x1b.lnrpc.PayReq.FeaturesEntry\x1a?\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x0e.lnrpc.Feature:\x02\x38\x01\">\n\x07\x46\x65\x61ture\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0bis_required\x18\x03 \x01(\x08\x12\x10\n\x08is_known\x18\x04 \x01(\x08\"\x12\n\x10\x46\x65\x65ReportRequest\"|\n\x10\x43hannelFeeReport\x12\x13\n\x07\x63han_id\x18\x05 \x01(\x04\x42\x02\x30\x01\x12\x15\n\rchannel_point\x18\x01 \x01(\t\x12\x15\n\rbase_fee_msat\x18\x02 \x01(\x03\x12\x13\n\x0b\x66\x65\x65_per_mil\x18\x03 \x01(\x03\x12\x10\n\x08\x66\x65\x65_rate\x18\x04 \x01(\x01\"\x84\x01\n\x11\x46\x65\x65ReportResponse\x12-\n\x0c\x63hannel_fees\x18\x01 \x03(\x0b\x32\x17.lnrpc.ChannelFeeReport\x12\x13\n\x0b\x64\x61y_fee_sum\x18\x02 \x01(\x04\x12\x14\n\x0cweek_fee_sum\x18\x03 \x01(\x04\x12\x15\n\rmonth_fee_sum\x18\x04 \x01(\x04\"\x82\x02\n\x13PolicyUpdateRequest\x12\x10\n\x06global\x18\x01 \x01(\x08H\x00\x12)\n\nchan_point\x18\x02 \x01(\x0b\x32\x13.lnrpc.ChannelPointH\x00\x12\x15\n\rbase_fee_msat\x18\x03 \x01(\x03\x12\x10\n\x08\x66\x65\x65_rate\x18\x04 \x01(\x01\x12\x14\n\x0c\x66\x65\x65_rate_ppm\x18\t \x01(\r\x12\x17\n\x0ftime_lock_delta\x18\x05 \x01(\r\x12\x15\n\rmax_htlc_msat\x18\x06 \x01(\x04\x12\x15\n\rmin_htlc_msat\x18\x07 \x01(\x04\x12\x1f\n\x17min_htlc_msat_specified\x18\x08 \x01(\x08\x42\x07\n\x05scope\"m\n\x0c\x46\x61iledUpdate\x12!\n\x08outpoint\x18\x01 \x01(\x0b\x32\x0f.lnrpc.OutPoint\x12$\n\x06reason\x18\x02 \x01(\x0e\x32\x14.lnrpc.UpdateFailure\x12\x14\n\x0cupdate_error\x18\x03 \x01(\t\"C\n\x14PolicyUpdateResponse\x12+\n\x0e\x66\x61iled_updates\x18\x01 \x03(\x0b\x32\x13.lnrpc.FailedUpdate\"\x89\x01\n\x18\x46orwardingHistoryRequest\x12\x12\n\nstart_time\x18\x01 \x01(\x04\x12\x10\n\x08\x65nd_time\x18\x02 \x01(\x04\x12\x14\n\x0cindex_offset\x18\x03 \x01(\r\x12\x16\n\x0enum_max_events\x18\x04 \x01(\r\x12\x19\n\x11peer_alias_lookup\x18\x05 \x01(\x08\"\x89\x02\n\x0f\x46orwardingEvent\x12\x15\n\ttimestamp\x18\x01 \x01(\x04\x42\x02\x18\x01\x12\x16\n\nchan_id_in\x18\x02 \x01(\x04\x42\x02\x30\x01\x12\x17\n\x0b\x63han_id_out\x18\x04 \x01(\x04\x42\x02\x30\x01\x12\x0e\n\x06\x61mt_in\x18\x05 \x01(\x04\x12\x0f\n\x07\x61mt_out\x18\x06 \x01(\x04\x12\x0b\n\x03\x66\x65\x65\x18\x07 \x01(\x04\x12\x10\n\x08\x66\x65\x65_msat\x18\x08 \x01(\x04\x12\x13\n\x0b\x61mt_in_msat\x18\t \x01(\x04\x12\x14\n\x0c\x61mt_out_msat\x18\n \x01(\x04\x12\x14\n\x0ctimestamp_ns\x18\x0b \x01(\x04\x12\x15\n\rpeer_alias_in\x18\x0c \x01(\t\x12\x16\n\x0epeer_alias_out\x18\r \x01(\t\"i\n\x19\x46orwardingHistoryResponse\x12\x31\n\x11\x66orwarding_events\x18\x01 \x03(\x0b\x32\x16.lnrpc.ForwardingEvent\x12\x19\n\x11last_offset_index\x18\x02 \x01(\r\"E\n\x1a\x45xportChannelBackupRequest\x12\'\n\nchan_point\x18\x01 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\"M\n\rChannelBackup\x12\'\n\nchan_point\x18\x01 \x01(\x0b\x32\x13.lnrpc.ChannelPoint\x12\x13\n\x0b\x63han_backup\x18\x02 \x01(\x0c\"V\n\x0fMultiChanBackup\x12(\n\x0b\x63han_points\x18\x01 \x03(\x0b\x32\x13.lnrpc.ChannelPoint\x12\x19\n\x11multi_chan_backup\x18\x02 \x01(\x0c\"\x19\n\x17\x43hanBackupExportRequest\"{\n\x12\x43hanBackupSnapshot\x12\x32\n\x13single_chan_backups\x18\x01 \x01(\x0b\x32\x15.lnrpc.ChannelBackups\x12\x31\n\x11multi_chan_backup\x18\x02 \x01(\x0b\x32\x16.lnrpc.MultiChanBackup\"<\n\x0e\x43hannelBackups\x12*\n\x0c\x63han_backups\x18\x01 \x03(\x0b\x32\x14.lnrpc.ChannelBackup\"p\n\x18RestoreChanBackupRequest\x12-\n\x0c\x63han_backups\x18\x01 \x01(\x0b\x32\x15.lnrpc.ChannelBackupsH\x00\x12\x1b\n\x11multi_chan_backup\x18\x02 \x01(\x0cH\x00\x42\x08\n\x06\x62\x61\x63kup\"\x17\n\x15RestoreBackupResponse\"\x1b\n\x19\x43hannelBackupSubscription\"\x1a\n\x18VerifyChanBackupResponse\"4\n\x12MacaroonPermission\x12\x0e\n\x06\x65ntity\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\"~\n\x13\x42\x61keMacaroonRequest\x12.\n\x0bpermissions\x18\x01 \x03(\x0b\x32\x19.lnrpc.MacaroonPermission\x12\x13\n\x0broot_key_id\x18\x02 \x01(\x04\x12\"\n\x1a\x61llow_external_permissions\x18\x03 \x01(\x08\"(\n\x14\x42\x61keMacaroonResponse\x12\x10\n\x08macaroon\x18\x01 \x01(\t\"\x18\n\x16ListMacaroonIDsRequest\"/\n\x17ListMacaroonIDsResponse\x12\x14\n\x0croot_key_ids\x18\x01 \x03(\x04\".\n\x17\x44\x65leteMacaroonIDRequest\x12\x13\n\x0broot_key_id\x18\x01 \x01(\x04\"+\n\x18\x44\x65leteMacaroonIDResponse\x12\x0f\n\x07\x64\x65leted\x18\x01 \x01(\x08\"H\n\x16MacaroonPermissionList\x12.\n\x0bpermissions\x18\x01 \x03(\x0b\x32\x19.lnrpc.MacaroonPermission\"\x18\n\x16ListPermissionsRequest\"\xc5\x01\n\x17ListPermissionsResponse\x12Q\n\x12method_permissions\x18\x01 \x03(\x0b\x32\x35.lnrpc.ListPermissionsResponse.MethodPermissionsEntry\x1aW\n\x16MethodPermissionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x1d.lnrpc.MacaroonPermissionList:\x02\x38\x01\"\xd5\x07\n\x07\x46\x61ilure\x12(\n\x04\x63ode\x18\x01 \x01(\x0e\x32\x1a.lnrpc.Failure.FailureCode\x12,\n\x0e\x63hannel_update\x18\x03 \x01(\x0b\x32\x14.lnrpc.ChannelUpdate\x12\x11\n\thtlc_msat\x18\x04 \x01(\x04\x12\x15\n\ronion_sha_256\x18\x05 \x01(\x0c\x12\x13\n\x0b\x63ltv_expiry\x18\x06 \x01(\r\x12\r\n\x05\x66lags\x18\x07 \x01(\r\x12\x1c\n\x14\x66\x61ilure_source_index\x18\x08 \x01(\r\x12\x0e\n\x06height\x18\t \x01(\r\"\xef\x05\n\x0b\x46\x61ilureCode\x12\x0c\n\x08RESERVED\x10\x00\x12(\n$INCORRECT_OR_UNKNOWN_PAYMENT_DETAILS\x10\x01\x12\x1c\n\x18INCORRECT_PAYMENT_AMOUNT\x10\x02\x12\x1f\n\x1b\x46INAL_INCORRECT_CLTV_EXPIRY\x10\x03\x12\x1f\n\x1b\x46INAL_INCORRECT_HTLC_AMOUNT\x10\x04\x12\x19\n\x15\x46INAL_EXPIRY_TOO_SOON\x10\x05\x12\x11\n\rINVALID_REALM\x10\x06\x12\x13\n\x0f\x45XPIRY_TOO_SOON\x10\x07\x12\x19\n\x15INVALID_ONION_VERSION\x10\x08\x12\x16\n\x12INVALID_ONION_HMAC\x10\t\x12\x15\n\x11INVALID_ONION_KEY\x10\n\x12\x18\n\x14\x41MOUNT_BELOW_MINIMUM\x10\x0b\x12\x14\n\x10\x46\x45\x45_INSUFFICIENT\x10\x0c\x12\x19\n\x15INCORRECT_CLTV_EXPIRY\x10\r\x12\x14\n\x10\x43HANNEL_DISABLED\x10\x0e\x12\x1d\n\x19TEMPORARY_CHANNEL_FAILURE\x10\x0f\x12!\n\x1dREQUIRED_NODE_FEATURE_MISSING\x10\x10\x12$\n REQUIRED_CHANNEL_FEATURE_MISSING\x10\x11\x12\x15\n\x11UNKNOWN_NEXT_PEER\x10\x12\x12\x1a\n\x16TEMPORARY_NODE_FAILURE\x10\x13\x12\x1a\n\x16PERMANENT_NODE_FAILURE\x10\x14\x12\x1d\n\x19PERMANENT_CHANNEL_FAILURE\x10\x15\x12\x12\n\x0e\x45XPIRY_TOO_FAR\x10\x16\x12\x0f\n\x0bMPP_TIMEOUT\x10\x17\x12\x19\n\x15INVALID_ONION_PAYLOAD\x10\x18\x12\x15\n\x10INTERNAL_FAILURE\x10\xe5\x07\x12\x14\n\x0fUNKNOWN_FAILURE\x10\xe6\x07\x12\x17\n\x12UNREADABLE_FAILURE\x10\xe7\x07J\x04\x08\x02\x10\x03\"\x9a\x02\n\rChannelUpdate\x12\x11\n\tsignature\x18\x01 \x01(\x0c\x12\x12\n\nchain_hash\x18\x02 \x01(\x0c\x12\x13\n\x07\x63han_id\x18\x03 \x01(\x04\x42\x02\x30\x01\x12\x11\n\ttimestamp\x18\x04 \x01(\r\x12\x15\n\rmessage_flags\x18\n \x01(\r\x12\x15\n\rchannel_flags\x18\x05 \x01(\r\x12\x17\n\x0ftime_lock_delta\x18\x06 \x01(\r\x12\x19\n\x11htlc_minimum_msat\x18\x07 \x01(\x04\x12\x10\n\x08\x62\x61se_fee\x18\x08 \x01(\r\x12\x10\n\x08\x66\x65\x65_rate\x18\t \x01(\r\x12\x19\n\x11htlc_maximum_msat\x18\x0b \x01(\x04\x12\x19\n\x11\x65xtra_opaque_data\x18\x0c \x01(\x0c\"F\n\nMacaroonId\x12\r\n\x05nonce\x18\x01 \x01(\x0c\x12\x11\n\tstorageId\x18\x02 \x01(\x0c\x12\x16\n\x03ops\x18\x03 \x03(\x0b\x32\t.lnrpc.Op\"%\n\x02Op\x12\x0e\n\x06\x65ntity\x18\x01 \x01(\t\x12\x0f\n\x07\x61\x63tions\x18\x02 \x03(\t\"k\n\x13\x43heckMacPermRequest\x12\x10\n\x08macaroon\x18\x01 \x01(\x0c\x12.\n\x0bpermissions\x18\x02 \x03(\x0b\x32\x19.lnrpc.MacaroonPermission\x12\x12\n\nfullMethod\x18\x03 \x01(\t\"%\n\x14\x43heckMacPermResponse\x12\r\n\x05valid\x18\x01 \x01(\x08\"\x92\x02\n\x14RPCMiddlewareRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x14\n\x0craw_macaroon\x18\x02 \x01(\x0c\x12\x1f\n\x17\x63ustom_caveat_condition\x18\x03 \x01(\t\x12(\n\x0bstream_auth\x18\x04 \x01(\x0b\x32\x11.lnrpc.StreamAuthH\x00\x12$\n\x07request\x18\x05 \x01(\x0b\x32\x11.lnrpc.RPCMessageH\x00\x12%\n\x08response\x18\x06 \x01(\x0b\x32\x11.lnrpc.RPCMessageH\x00\x12\x16\n\x0creg_complete\x18\x08 \x01(\x08H\x00\x12\x0e\n\x06msg_id\x18\x07 \x01(\x04\x42\x10\n\x0eintercept_type\"%\n\nStreamAuth\x12\x17\n\x0fmethod_full_uri\x18\x01 \x01(\t\"r\n\nRPCMessage\x12\x17\n\x0fmethod_full_uri\x18\x01 \x01(\t\x12\x12\n\nstream_rpc\x18\x02 \x01(\x08\x12\x11\n\ttype_name\x18\x03 \x01(\t\x12\x12\n\nserialized\x18\x04 \x01(\x0c\x12\x10\n\x08is_error\x18\x05 \x01(\x08\"\xa2\x01\n\x15RPCMiddlewareResponse\x12\x12\n\nref_msg_id\x18\x01 \x01(\x04\x12\x31\n\x08register\x18\x02 \x01(\x0b\x32\x1d.lnrpc.MiddlewareRegistrationH\x00\x12,\n\x08\x66\x65\x65\x64\x62\x61\x63k\x18\x03 \x01(\x0b\x32\x18.lnrpc.InterceptFeedbackH\x00\x42\x14\n\x12middleware_message\"n\n\x16MiddlewareRegistration\x12\x17\n\x0fmiddleware_name\x18\x01 \x01(\t\x12#\n\x1b\x63ustom_macaroon_caveat_name\x18\x02 \x01(\t\x12\x16\n\x0eread_only_mode\x18\x03 \x01(\x08\"\\\n\x11InterceptFeedback\x12\r\n\x05\x65rror\x18\x01 \x01(\t\x12\x18\n\x10replace_response\x18\x02 \x01(\x08\x12\x1e\n\x16replacement_serialized\x18\x03 \x01(\x0c*\xcb\x02\n\x10OutputScriptType\x12\x1b\n\x17SCRIPT_TYPE_PUBKEY_HASH\x10\x00\x12\x1b\n\x17SCRIPT_TYPE_SCRIPT_HASH\x10\x01\x12&\n\"SCRIPT_TYPE_WITNESS_V0_PUBKEY_HASH\x10\x02\x12&\n\"SCRIPT_TYPE_WITNESS_V0_SCRIPT_HASH\x10\x03\x12\x16\n\x12SCRIPT_TYPE_PUBKEY\x10\x04\x12\x18\n\x14SCRIPT_TYPE_MULTISIG\x10\x05\x12\x18\n\x14SCRIPT_TYPE_NULLDATA\x10\x06\x12\x1c\n\x18SCRIPT_TYPE_NON_STANDARD\x10\x07\x12\x1f\n\x1bSCRIPT_TYPE_WITNESS_UNKNOWN\x10\x08\x12\"\n\x1eSCRIPT_TYPE_WITNESS_V1_TAPROOT\x10\t*\xac\x01\n\x0b\x41\x64\x64ressType\x12\x17\n\x13WITNESS_PUBKEY_HASH\x10\x00\x12\x16\n\x12NESTED_PUBKEY_HASH\x10\x01\x12\x1e\n\x1aUNUSED_WITNESS_PUBKEY_HASH\x10\x02\x12\x1d\n\x19UNUSED_NESTED_PUBKEY_HASH\x10\x03\x12\x12\n\x0eTAPROOT_PUBKEY\x10\x04\x12\x19\n\x15UNUSED_TAPROOT_PUBKEY\x10\x05*\x8c\x01\n\x0e\x43ommitmentType\x12\x1b\n\x17UNKNOWN_COMMITMENT_TYPE\x10\x00\x12\n\n\x06LEGACY\x10\x01\x12\x15\n\x11STATIC_REMOTE_KEY\x10\x02\x12\x0b\n\x07\x41NCHORS\x10\x03\x12\x19\n\x15SCRIPT_ENFORCED_LEASE\x10\x04\x12\x12\n\x0eSIMPLE_TAPROOT\x10\x05*a\n\tInitiator\x12\x15\n\x11INITIATOR_UNKNOWN\x10\x00\x12\x13\n\x0fINITIATOR_LOCAL\x10\x01\x12\x14\n\x10INITIATOR_REMOTE\x10\x02\x12\x12\n\x0eINITIATOR_BOTH\x10\x03*`\n\x0eResolutionType\x12\x10\n\x0cTYPE_UNKNOWN\x10\x00\x12\n\n\x06\x41NCHOR\x10\x01\x12\x11\n\rINCOMING_HTLC\x10\x02\x12\x11\n\rOUTGOING_HTLC\x10\x03\x12\n\n\x06\x43OMMIT\x10\x04*q\n\x11ResolutionOutcome\x12\x13\n\x0fOUTCOME_UNKNOWN\x10\x00\x12\x0b\n\x07\x43LAIMED\x10\x01\x12\r\n\tUNCLAIMED\x10\x02\x12\r\n\tABANDONED\x10\x03\x12\x0f\n\x0b\x46IRST_STAGE\x10\x04\x12\x0b\n\x07TIMEOUT\x10\x05*9\n\x0eNodeMetricType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x1a\n\x16\x42\x45TWEENNESS_CENTRALITY\x10\x01*;\n\x10InvoiceHTLCState\x12\x0c\n\x08\x41\x43\x43\x45PTED\x10\x00\x12\x0b\n\x07SETTLED\x10\x01\x12\x0c\n\x08\x43\x41NCELED\x10\x02*\xd9\x01\n\x14PaymentFailureReason\x12\x17\n\x13\x46\x41ILURE_REASON_NONE\x10\x00\x12\x1a\n\x16\x46\x41ILURE_REASON_TIMEOUT\x10\x01\x12\x1b\n\x17\x46\x41ILURE_REASON_NO_ROUTE\x10\x02\x12\x18\n\x14\x46\x41ILURE_REASON_ERROR\x10\x03\x12,\n(FAILURE_REASON_INCORRECT_PAYMENT_DETAILS\x10\x04\x12\'\n#FAILURE_REASON_INSUFFICIENT_BALANCE\x10\x05*\xcf\x04\n\nFeatureBit\x12\x18\n\x14\x44\x41TALOSS_PROTECT_REQ\x10\x00\x12\x18\n\x14\x44\x41TALOSS_PROTECT_OPT\x10\x01\x12\x17\n\x13INITIAL_ROUING_SYNC\x10\x03\x12\x1f\n\x1bUPFRONT_SHUTDOWN_SCRIPT_REQ\x10\x04\x12\x1f\n\x1bUPFRONT_SHUTDOWN_SCRIPT_OPT\x10\x05\x12\x16\n\x12GOSSIP_QUERIES_REQ\x10\x06\x12\x16\n\x12GOSSIP_QUERIES_OPT\x10\x07\x12\x11\n\rTLV_ONION_REQ\x10\x08\x12\x11\n\rTLV_ONION_OPT\x10\t\x12\x1a\n\x16\x45XT_GOSSIP_QUERIES_REQ\x10\n\x12\x1a\n\x16\x45XT_GOSSIP_QUERIES_OPT\x10\x0b\x12\x19\n\x15STATIC_REMOTE_KEY_REQ\x10\x0c\x12\x19\n\x15STATIC_REMOTE_KEY_OPT\x10\r\x12\x14\n\x10PAYMENT_ADDR_REQ\x10\x0e\x12\x14\n\x10PAYMENT_ADDR_OPT\x10\x0f\x12\x0b\n\x07MPP_REQ\x10\x10\x12\x0b\n\x07MPP_OPT\x10\x11\x12\x16\n\x12WUMBO_CHANNELS_REQ\x10\x12\x12\x16\n\x12WUMBO_CHANNELS_OPT\x10\x13\x12\x0f\n\x0b\x41NCHORS_REQ\x10\x14\x12\x0f\n\x0b\x41NCHORS_OPT\x10\x15\x12\x1d\n\x19\x41NCHORS_ZERO_FEE_HTLC_REQ\x10\x16\x12\x1d\n\x19\x41NCHORS_ZERO_FEE_HTLC_OPT\x10\x17\x12\x0b\n\x07\x41MP_REQ\x10\x1e\x12\x0b\n\x07\x41MP_OPT\x10\x1f*\xac\x01\n\rUpdateFailure\x12\x1a\n\x16UPDATE_FAILURE_UNKNOWN\x10\x00\x12\x1a\n\x16UPDATE_FAILURE_PENDING\x10\x01\x12\x1c\n\x18UPDATE_FAILURE_NOT_FOUND\x10\x02\x12\x1f\n\x1bUPDATE_FAILURE_INTERNAL_ERR\x10\x03\x12$\n UPDATE_FAILURE_INVALID_PARAMETER\x10\x04\x32\xf0&\n\tLightning\x12J\n\rWalletBalance\x12\x1b.lnrpc.WalletBalanceRequest\x1a\x1c.lnrpc.WalletBalanceResponse\x12M\n\x0e\x43hannelBalance\x12\x1c.lnrpc.ChannelBalanceRequest\x1a\x1d.lnrpc.ChannelBalanceResponse\x12K\n\x0fGetTransactions\x12\x1d.lnrpc.GetTransactionsRequest\x1a\x19.lnrpc.TransactionDetails\x12\x44\n\x0b\x45stimateFee\x12\x19.lnrpc.EstimateFeeRequest\x1a\x1a.lnrpc.EstimateFeeResponse\x12>\n\tSendCoins\x12\x17.lnrpc.SendCoinsRequest\x1a\x18.lnrpc.SendCoinsResponse\x12\x44\n\x0bListUnspent\x12\x19.lnrpc.ListUnspentRequest\x1a\x1a.lnrpc.ListUnspentResponse\x12L\n\x15SubscribeTransactions\x12\x1d.lnrpc.GetTransactionsRequest\x1a\x12.lnrpc.Transaction0\x01\x12;\n\x08SendMany\x12\x16.lnrpc.SendManyRequest\x1a\x17.lnrpc.SendManyResponse\x12\x41\n\nNewAddress\x12\x18.lnrpc.NewAddressRequest\x1a\x19.lnrpc.NewAddressResponse\x12\x44\n\x0bSignMessage\x12\x19.lnrpc.SignMessageRequest\x1a\x1a.lnrpc.SignMessageResponse\x12J\n\rVerifyMessage\x12\x1b.lnrpc.VerifyMessageRequest\x1a\x1c.lnrpc.VerifyMessageResponse\x12\x44\n\x0b\x43onnectPeer\x12\x19.lnrpc.ConnectPeerRequest\x1a\x1a.lnrpc.ConnectPeerResponse\x12M\n\x0e\x44isconnectPeer\x12\x1c.lnrpc.DisconnectPeerRequest\x1a\x1d.lnrpc.DisconnectPeerResponse\x12>\n\tListPeers\x12\x17.lnrpc.ListPeersRequest\x1a\x18.lnrpc.ListPeersResponse\x12G\n\x13SubscribePeerEvents\x12\x1c.lnrpc.PeerEventSubscription\x1a\x10.lnrpc.PeerEvent0\x01\x12\x38\n\x07GetInfo\x12\x15.lnrpc.GetInfoRequest\x1a\x16.lnrpc.GetInfoResponse\x12P\n\x0fGetRecoveryInfo\x12\x1d.lnrpc.GetRecoveryInfoRequest\x1a\x1e.lnrpc.GetRecoveryInfoResponse\x12P\n\x0fPendingChannels\x12\x1d.lnrpc.PendingChannelsRequest\x1a\x1e.lnrpc.PendingChannelsResponse\x12G\n\x0cListChannels\x12\x1a.lnrpc.ListChannelsRequest\x1a\x1b.lnrpc.ListChannelsResponse\x12V\n\x16SubscribeChannelEvents\x12\x1f.lnrpc.ChannelEventSubscription\x1a\x19.lnrpc.ChannelEventUpdate0\x01\x12M\n\x0e\x43losedChannels\x12\x1c.lnrpc.ClosedChannelsRequest\x1a\x1d.lnrpc.ClosedChannelsResponse\x12\x41\n\x0fOpenChannelSync\x12\x19.lnrpc.OpenChannelRequest\x1a\x13.lnrpc.ChannelPoint\x12\x43\n\x0bOpenChannel\x12\x19.lnrpc.OpenChannelRequest\x1a\x17.lnrpc.OpenStatusUpdate0\x01\x12S\n\x10\x42\x61tchOpenChannel\x12\x1e.lnrpc.BatchOpenChannelRequest\x1a\x1f.lnrpc.BatchOpenChannelResponse\x12L\n\x10\x46undingStateStep\x12\x1b.lnrpc.FundingTransitionMsg\x1a\x1b.lnrpc.FundingStateStepResp\x12P\n\x0f\x43hannelAcceptor\x12\x1c.lnrpc.ChannelAcceptResponse\x1a\x1b.lnrpc.ChannelAcceptRequest(\x01\x30\x01\x12\x46\n\x0c\x43loseChannel\x12\x1a.lnrpc.CloseChannelRequest\x1a\x18.lnrpc.CloseStatusUpdate0\x01\x12M\n\x0e\x41\x62\x61ndonChannel\x12\x1c.lnrpc.AbandonChannelRequest\x1a\x1d.lnrpc.AbandonChannelResponse\x12?\n\x0bSendPayment\x12\x12.lnrpc.SendRequest\x1a\x13.lnrpc.SendResponse\"\x03\x88\x02\x01(\x01\x30\x01\x12:\n\x0fSendPaymentSync\x12\x12.lnrpc.SendRequest\x1a\x13.lnrpc.SendResponse\x12\x46\n\x0bSendToRoute\x12\x19.lnrpc.SendToRouteRequest\x1a\x13.lnrpc.SendResponse\"\x03\x88\x02\x01(\x01\x30\x01\x12\x41\n\x0fSendToRouteSync\x12\x19.lnrpc.SendToRouteRequest\x1a\x13.lnrpc.SendResponse\x12\x37\n\nAddInvoice\x12\x0e.lnrpc.Invoice\x1a\x19.lnrpc.AddInvoiceResponse\x12\x45\n\x0cListInvoices\x12\x19.lnrpc.ListInvoiceRequest\x1a\x1a.lnrpc.ListInvoiceResponse\x12\x33\n\rLookupInvoice\x12\x12.lnrpc.PaymentHash\x1a\x0e.lnrpc.Invoice\x12\x41\n\x11SubscribeInvoices\x12\x1a.lnrpc.InvoiceSubscription\x1a\x0e.lnrpc.Invoice0\x01\x12\x32\n\x0c\x44\x65\x63odePayReq\x12\x13.lnrpc.PayReqString\x1a\r.lnrpc.PayReq\x12G\n\x0cListPayments\x12\x1a.lnrpc.ListPaymentsRequest\x1a\x1b.lnrpc.ListPaymentsResponse\x12J\n\rDeletePayment\x12\x1b.lnrpc.DeletePaymentRequest\x1a\x1c.lnrpc.DeletePaymentResponse\x12V\n\x11\x44\x65leteAllPayments\x12\x1f.lnrpc.DeleteAllPaymentsRequest\x1a .lnrpc.DeleteAllPaymentsResponse\x12@\n\rDescribeGraph\x12\x1a.lnrpc.ChannelGraphRequest\x1a\x13.lnrpc.ChannelGraph\x12G\n\x0eGetNodeMetrics\x12\x19.lnrpc.NodeMetricsRequest\x1a\x1a.lnrpc.NodeMetricsResponse\x12\x39\n\x0bGetChanInfo\x12\x16.lnrpc.ChanInfoRequest\x1a\x12.lnrpc.ChannelEdge\x12\x36\n\x0bGetNodeInfo\x12\x16.lnrpc.NodeInfoRequest\x1a\x0f.lnrpc.NodeInfo\x12\x44\n\x0bQueryRoutes\x12\x19.lnrpc.QueryRoutesRequest\x1a\x1a.lnrpc.QueryRoutesResponse\x12?\n\x0eGetNetworkInfo\x12\x19.lnrpc.NetworkInfoRequest\x1a\x12.lnrpc.NetworkInfo\x12\x35\n\nStopDaemon\x12\x12.lnrpc.StopRequest\x1a\x13.lnrpc.StopResponse\x12W\n\x15SubscribeChannelGraph\x12 .lnrpc.GraphTopologySubscription\x1a\x1a.lnrpc.GraphTopologyUpdate0\x01\x12\x41\n\nDebugLevel\x12\x18.lnrpc.DebugLevelRequest\x1a\x19.lnrpc.DebugLevelResponse\x12>\n\tFeeReport\x12\x17.lnrpc.FeeReportRequest\x1a\x18.lnrpc.FeeReportResponse\x12N\n\x13UpdateChannelPolicy\x12\x1a.lnrpc.PolicyUpdateRequest\x1a\x1b.lnrpc.PolicyUpdateResponse\x12V\n\x11\x46orwardingHistory\x12\x1f.lnrpc.ForwardingHistoryRequest\x1a .lnrpc.ForwardingHistoryResponse\x12N\n\x13\x45xportChannelBackup\x12!.lnrpc.ExportChannelBackupRequest\x1a\x14.lnrpc.ChannelBackup\x12T\n\x17\x45xportAllChannelBackups\x12\x1e.lnrpc.ChanBackupExportRequest\x1a\x19.lnrpc.ChanBackupSnapshot\x12N\n\x10VerifyChanBackup\x12\x19.lnrpc.ChanBackupSnapshot\x1a\x1f.lnrpc.VerifyChanBackupResponse\x12V\n\x15RestoreChannelBackups\x12\x1f.lnrpc.RestoreChanBackupRequest\x1a\x1c.lnrpc.RestoreBackupResponse\x12X\n\x17SubscribeChannelBackups\x12 .lnrpc.ChannelBackupSubscription\x1a\x19.lnrpc.ChanBackupSnapshot0\x01\x12G\n\x0c\x42\x61keMacaroon\x12\x1a.lnrpc.BakeMacaroonRequest\x1a\x1b.lnrpc.BakeMacaroonResponse\x12P\n\x0fListMacaroonIDs\x12\x1d.lnrpc.ListMacaroonIDsRequest\x1a\x1e.lnrpc.ListMacaroonIDsResponse\x12S\n\x10\x44\x65leteMacaroonID\x12\x1e.lnrpc.DeleteMacaroonIDRequest\x1a\x1f.lnrpc.DeleteMacaroonIDResponse\x12P\n\x0fListPermissions\x12\x1d.lnrpc.ListPermissionsRequest\x1a\x1e.lnrpc.ListPermissionsResponse\x12S\n\x18\x43heckMacaroonPermissions\x12\x1a.lnrpc.CheckMacPermRequest\x1a\x1b.lnrpc.CheckMacPermResponse\x12V\n\x15RegisterRPCMiddleware\x12\x1c.lnrpc.RPCMiddlewareResponse\x1a\x1b.lnrpc.RPCMiddlewareRequest(\x01\x30\x01\x12V\n\x11SendCustomMessage\x12\x1f.lnrpc.SendCustomMessageRequest\x1a .lnrpc.SendCustomMessageResponse\x12X\n\x17SubscribeCustomMessages\x12%.lnrpc.SubscribeCustomMessagesRequest\x1a\x14.lnrpc.CustomMessage0\x01\x12\x44\n\x0bListAliases\x12\x19.lnrpc.ListAliasesRequest\x1a\x1a.lnrpc.ListAliasesResponse\x12_\n\x14LookupHtlcResolution\x12\".lnrpc.LookupHtlcResolutionRequest\x1a#.lnrpc.LookupHtlcResolutionResponseB\'Z%github.com/lightningnetwork/lnd/lnrpcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lightning_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z%github.com/lightningnetwork/lnd/lnrpc'
  _TRANSACTION.fields_by_name['dest_addresses']._options = None
  _TRANSACTION.fields_by_name['dest_addresses']._serialized_options = b'\030\001'
  _SENDREQUEST_DESTCUSTOMRECORDSENTRY._options = None
  _SENDREQUEST_DESTCUSTOMRECORDSENTRY._serialized_options = b'8\001'
  _SENDREQUEST.fields_by_name['dest_string']._options = None
  _SENDREQUEST.fields_by_name['dest_string']._serialized_options = b'\030\001'
  _SENDREQUEST.fields_by_name['payment_hash_string']._options = None
  _SENDREQUEST.fields_by_name['payment_hash_string']._serialized_options = b'\030\001'
  _SENDREQUEST.fields_by_name['outgoing_chan_id']._options = None
  _SENDREQUEST.fields_by_name['outgoing_chan_id']._serialized_options = b'0\001'
  _SENDTOROUTEREQUEST.fields_by_name['payment_hash_string']._options = None
  _SENDTOROUTEREQUEST.fields_by_name['payment_hash_string']._serialized_options = b'\030\001'
  _ESTIMATEFEEREQUEST_ADDRTOAMOUNTENTRY._options = None
  _ESTIMATEFEEREQUEST_ADDRTOAMOUNTENTRY._serialized_options = b'8\001'
  _ESTIMATEFEERESPONSE.fields_by_name['feerate_sat_per_byte']._options = None
  _ESTIMATEFEERESPONSE.fields_by_name['feerate_sat_per_byte']._serialized_options = b'\030\001'
  _SENDMANYREQUEST_ADDRTOAMOUNTENTRY._options = None
  _SENDMANYREQUEST_ADDRTOAMOUNTENTRY._serialized_options = b'8\001'
  _SENDMANYREQUEST.fields_by_name['sat_per_byte']._options = None
  _SENDMANYREQUEST.fields_by_name['sat_per_byte']._serialized_options = b'\030\001'
  _SENDCOINSREQUEST.fields_by_name['sat_per_byte']._options = None
  _SENDCOINSREQUEST.fields_by_name['sat_per_byte']._serialized_options = b'\030\001'
  _CHANNEL.fields_by_name['chan_id']._options = None
  _CHANNEL.fields_by_name['chan_id']._serialized_options = b'0\001'
  _CHANNEL.fields_by_name['csv_delay']._options = None
  _CHANNEL.fields_by_name['csv_delay']._serialized_options = b'\030\001'
  _CHANNEL.fields_by_name['local_chan_reserve_sat']._options = None
  _CHANNEL.fields_by_name['local_chan_reserve_sat']._serialized_options = b'\030\001'
  _CHANNEL.fields_by_name['remote_chan_reserve_sat']._options = None
  _CHANNEL.fields_by_name['remote_chan_reserve_sat']._serialized_options = b'\030\001'
  _CHANNEL.fields_by_name['static_remote_key']._options = None
  _CHANNEL.fields_by_name['static_remote_key']._serialized_options = b'\030\001'
  _CHANNEL.fields_by_name['peer_scid_alias']._options = None
  _CHANNEL.fields_by_name['peer_scid_alias']._serialized_options = b'0\001'
  _CHANNELCLOSESUMMARY.fields_by_name['chan_id']._options = None
  _CHANNELCLOSESUMMARY.fields_by_name['chan_id']._serialized_options = b'0\001'
  _CHANNELCLOSESUMMARY.fields_by_name['zero_conf_confirmed_scid']._options = None
  _CHANNELCLOSESUMMARY.fields_by_name['zero_conf_confirmed_scid']._serialized_options = b'0\001'
  _PEER_FEATURESENTRY._options = None
  _PEER_FEATURESENTRY._serialized_options = b'8\001'
  _GETINFORESPONSE_FEATURESENTRY._options = None
  _GETINFORESPONSE_FEATURESENTRY._serialized_options = b'8\001'
  _GETINFORESPONSE.fields_by_name['testnet']._options = None
  _GETINFORESPONSE.fields_by_name['testnet']._serialized_options = b'\030\001'
  _GETINFORESPONSE.fields_by_name['chains']._options = None
  _GETINFORESPONSE.fields_by_name['chains']._serialized_options = b'\030\001'
  _CHAIN.fields_by_name['chain']._options = None
  _CHAIN.fields_by_name['chain']._serialized_options = b'\030\001'
  _CLOSECHANNELREQUEST.fields_by_name['sat_per_byte']._options = None
  _CLOSECHANNELREQUEST.fields_by_name['sat_per_byte']._serialized_options = b'\030\001'
  _OPENCHANNELREQUEST.fields_by_name['node_pubkey_string']._options = None
  _OPENCHANNELREQUEST.fields_by_name['node_pubkey_string']._serialized_options = b'\030\001'
  _OPENCHANNELREQUEST.fields_by_name['sat_per_byte']._options = None
  _OPENCHANNELREQUEST.fields_by_name['sat_per_byte']._serialized_options = b'\030\001'
  _PENDINGCHANNELSRESPONSE.fields_by_name['pending_closing_channels']._options = None
  _PENDINGCHANNELSRESPONSE.fields_by_name['pending_closing_channels']._serialized_options = b'\030\001'
  _WALLETBALANCERESPONSE_ACCOUNTBALANCEENTRY._options = None
  _WALLETBALANCERESPONSE_ACCOUNTBALANCEENTRY._serialized_options = b'8\001'
  _CHANNELBALANCERESPONSE.fields_by_name['balance']._options = None
  _CHANNELBALANCERESPONSE.fields_by_name['balance']._serialized_options = b'\030\001'
  _CHANNELBALANCERESPONSE.fields_by_name['pending_open_balance']._options = None
  _CHANNELBALANCERESPONSE.fields_by_name['pending_open_balance']._serialized_options = b'\030\001'
  _QUERYROUTESREQUEST_DESTCUSTOMRECORDSENTRY._options = None
  _QUERYROUTESREQUEST_DESTCUSTOMRECORDSENTRY._serialized_options = b'8\001'
  _QUERYROUTESREQUEST.fields_by_name['ignored_edges']._options = None
  _QUERYROUTESREQUEST.fields_by_name['ignored_edges']._serialized_options = b'\030\001'
  _QUERYROUTESREQUEST.fields_by_name['outgoing_chan_id']._options = None
  _QUERYROUTESREQUEST.fields_by_name['outgoing_chan_id']._serialized_options = b'0\001'
  _EDGELOCATOR.fields_by_name['channel_id']._options = None
  _EDGELOCATOR.fields_by_name['channel_id']._serialized_options = b'0\001'
  _HOP_CUSTOMRECORDSENTRY._options = None
  _HOP_CUSTOMRECORDSENTRY._serialized_options = b'8\001'
  _HOP.fields_by_name['chan_id']._options = None
  _HOP.fields_by_name['chan_id']._serialized_options = b'0\001'
  _HOP.fields_by_name['chan_capacity']._options = None
  _HOP.fields_by_name['chan_capacity']._serialized_options = b'\030\001'
  _HOP.fields_by_name['amt_to_forward']._options = None
  _HOP.fields_by_name['amt_to_forward']._serialized_options = b'\030\001'
  _HOP.fields_by_name['fee']._options = None
  _HOP.fields_by_name['fee']._serialized_options = b'\030\001'
  _HOP.fields_by_name['tlv_payload']._options = None
  _HOP.fields_by_name['tlv_payload']._serialized_options = b'\030\001'
  _ROUTE.fields_by_name['total_fees']._options = None
  _ROUTE.fields_by_name['total_fees']._serialized_options = b'\030\001'
  _ROUTE.fields_by_name['total_amt']._options = None
  _ROUTE.fields_by_name['total_amt']._serialized_options = b'\030\001'
  _LIGHTNINGNODE_FEATURESENTRY._options = None
  _LIGHTNINGNODE_FEATURESENTRY._serialized_options = b'8\001'
  _LIGHTNINGNODE_CUSTOMRECORDSENTRY._options = None
  _LIGHTNINGNODE_CUSTOMRECORDSENTRY._serialized_options = b'8\001'
  _ROUTINGPOLICY_CUSTOMRECORDSENTRY._options = None
  _ROUTINGPOLICY_CUSTOMRECORDSENTRY._serialized_options = b'8\001'
  _CHANNELEDGE_CUSTOMRECORDSENTRY._options = None
  _CHANNELEDGE_CUSTOMRECORDSENTRY._serialized_options = b'8\001'
  _CHANNELEDGE.fields_by_name['channel_id']._options = None
  _CHANNELEDGE.fields_by_name['channel_id']._serialized_options = b'0\001'
  _CHANNELEDGE.fields_by_name['last_update']._options = None
  _CHANNELEDGE.fields_by_name['last_update']._serialized_options = b'\030\001'
  _NODEMETRICSRESPONSE_BETWEENNESSCENTRALITYENTRY._options = None
  _NODEMETRICSRESPONSE_BETWEENNESSCENTRALITYENTRY._serialized_options = b'8\001'
  _CHANINFOREQUEST.fields_by_name['chan_id']._options = None
  _CHANINFOREQUEST.fields_by_name['chan_id']._serialized_options = b'0\001'
  _NODEUPDATE_FEATURESENTRY._options = None
  _NODEUPDATE_FEATURESENTRY._serialized_options = b'8\001'
  _NODEUPDATE.fields_by_name['addresses']._options = None
  _NODEUPDATE.fields_by_name['addresses']._serialized_options = b'\030\001'
  _NODEUPDATE.fields_by_name['global_features']._options = None
  _NODEUPDATE.fields_by_name['global_features']._serialized_options = b'\030\001'
  _CHANNELEDGEUPDATE.fields_by_name['chan_id']._options = None
  _CHANNELEDGEUPDATE.fields_by_name['chan_id']._serialized_options = b'0\001'
  _CLOSEDCHANNELUPDATE.fields_by_name['chan_id']._options = None
  _CLOSEDCHANNELUPDATE.fields_by_name['chan_id']._serialized_options = b'0\001'
  _HOPHINT.fields_by_name['chan_id']._options = None
  _HOPHINT.fields_by_name['chan_id']._serialized_options = b'0\001'
  _INVOICE_FEATURESENTRY._options = None
  _INVOICE_FEATURESENTRY._serialized_options = b'8\001'
  _INVOICE_AMPINVOICESTATEENTRY._options = None
  _INVOICE_AMPINVOICESTATEENTRY._serialized_options = b'8\001'
  _INVOICE.fields_by_name['settled']._options = None
  _INVOICE.fields_by_name['settled']._serialized_options = b'\030\001'
  _INVOICE.fields_by_name['amt_paid']._options = None
  _INVOICE.fields_by_name['amt_paid']._serialized_options = b'\030\001'
  _INVOICEHTLC_CUSTOMRECORDSENTRY._options = None
  _INVOICEHTLC_CUSTOMRECORDSENTRY._serialized_options = b'8\001'
  _INVOICEHTLC.fields_by_name['chan_id']._options = None
  _INVOICEHTLC.fields_by_name['chan_id']._serialized_options = b'0\001'
  _PAYMENTHASH.fields_by_name['r_hash_str']._options = None
  _PAYMENTHASH.fields_by_name['r_hash_str']._serialized_options = b'\030\001'
  _PAYMENT_PAYMENTSTATUS.values_by_name["UNKNOWN"]._options = None
  _PAYMENT_PAYMENTSTATUS.values_by_name["UNKNOWN"]._serialized_options = b'\010\001'
  _PAYMENT.fields_by_name['value']._options = None
  _PAYMENT.fields_by_name['value']._serialized_options = b'\030\001'
  _PAYMENT.fields_by_name['creation_date']._options = None
  _PAYMENT.fields_by_name['creation_date']._serialized_options = b'\030\001'
  _PAYMENT.fields_by_name['fee']._options = None
  _PAYMENT.fields_by_name['fee']._serialized_options = b'\030\001'
  _PAYREQ_FEATURESENTRY._options = None
  _PAYREQ_FEATURESENTRY._serialized_options = b'8\001'
  _CHANNELFEEREPORT.fields_by_name['chan_id']._options = None
  _CHANNELFEEREPORT.fields_by_name['chan_id']._serialized_options = b'0\001'
  _FORWARDINGEVENT.fields_by_name['timestamp']._options = None
  _FORWARDINGEVENT.fields_by_name['timestamp']._serialized_options = b'\030\001'
  _FORWARDINGEVENT.fields_by_name['chan_id_in']._options = None
  _FORWARDINGEVENT.fields_by_name['chan_id_in']._serialized_options = b'0\001'
  _FORWARDINGEVENT.fields_by_name['chan_id_out']._options = None
  _FORWARDINGEVENT.fields_by_name['chan_id_out']._serialized_options = b'0\001'
  _LISTPERMISSIONSRESPONSE_METHODPERMISSIONSENTRY._options = None
  _LISTPERMISSIONSRESPONSE_METHODPERMISSIONSENTRY._serialized_options = b'8\001'
  _CHANNELUPDATE.fields_by_name['chan_id']._options = None
  _CHANNELUPDATE.fields_by_name['chan_id']._serialized_options = b'0\001'
  _LIGHTNING.methods_by_name['SendPayment']._options = None
  _LIGHTNING.methods_by_name['SendPayment']._serialized_options = b'\210\002\001'
  _LIGHTNING.methods_by_name['SendToRoute']._options = None
  _LIGHTNING.methods_by_name['SendToRoute']._serialized_options = b'\210\002\001'
  _globals['_OUTPUTSCRIPTTYPE']._serialized_start=29557
  _globals['_OUTPUTSCRIPTTYPE']._serialized_end=29888
  _globals['_ADDRESSTYPE']._serialized_start=29891
  _globals['_ADDRESSTYPE']._serialized_end=30063
  _globals['_COMMITMENTTYPE']._serialized_start=30066
  _globals['_COMMITMENTTYPE']._serialized_end=30206
  _globals['_INITIATOR']._serialized_start=30208
  _globals['_INITIATOR']._serialized_end=30305
  _globals['_RESOLUTIONTYPE']._serialized_start=30307
  _globals['_RESOLUTIONTYPE']._serialized_end=30403
  _globals['_RESOLUTIONOUTCOME']._serialized_start=30405
  _globals['_RESOLUTIONOUTCOME']._serialized_end=30518
  _globals['_NODEMETRICTYPE']._serialized_start=30520
  _globals['_NODEMETRICTYPE']._serialized_end=30577
  _globals['_INVOICEHTLCSTATE']._serialized_start=30579
  _globals['_INVOICEHTLCSTATE']._serialized_end=30638
  _globals['_PAYMENTFAILUREREASON']._serialized_start=30641
  _globals['_PAYMENTFAILUREREASON']._serialized_end=30858
  _globals['_FEATUREBIT']._serialized_start=30861
  _globals['_FEATUREBIT']._serialized_end=31452
  _globals['_UPDATEFAILURE']._serialized_start=31455
  _globals['_UPDATEFAILURE']._serialized_end=31627
  _globals['_LOOKUPHTLCRESOLUTIONREQUEST']._serialized_start=26
  _globals['_LOOKUPHTLCRESOLUTIONREQUEST']._serialized_end=92
  _globals['_LOOKUPHTLCRESOLUTIONRESPONSE']._serialized_start=94
  _globals['_LOOKUPHTLCRESOLUTIONRESPONSE']._serialized_end=159
  _globals['_SUBSCRIBECUSTOMMESSAGESREQUEST']._serialized_start=161
  _globals['_SUBSCRIBECUSTOMMESSAGESREQUEST']._serialized_end=193
  _globals['_CUSTOMMESSAGE']._serialized_start=195
  _globals['_CUSTOMMESSAGE']._serialized_end=252
  _globals['_SENDCUSTOMMESSAGEREQUEST']._serialized_start=254
  _globals['_SENDCUSTOMMESSAGEREQUEST']._serialized_end=322
  _globals['_SENDCUSTOMMESSAGERESPONSE']._serialized_start=324
  _globals['_SENDCUSTOMMESSAGERESPONSE']._serialized_end=351
  _globals['_UTXO']._serialized_start=354
  _globals['_UTXO']._serialized_end=516
  _globals['_OUTPUTDETAIL']._serialized_start=519
  _globals['_OUTPUTDETAIL']._serialized_end=677
  _globals['_TRANSACTION']._serialized_start=680
  _globals['_TRANSACTION']._serialized_end=996
  _globals['_GETTRANSACTIONSREQUEST']._serialized_start=998
  _globals['_GETTRANSACTIONSREQUEST']._serialized_end=1081
  _globals['_TRANSACTIONDETAILS']._serialized_start=1083
  _globals['_TRANSACTIONDETAILS']._serialized_end=1145
  _globals['_FEELIMIT']._serialized_start=1147
  _globals['_FEELIMIT']._serialized_end=1224
  _globals['_SENDREQUEST']._serialized_start=1227
  _globals['_SENDREQUEST']._serialized_end=1749
  _globals['_SENDREQUEST_DESTCUSTOMRECORDSENTRY']._serialized_start=1693
  _globals['_SENDREQUEST_DESTCUSTOMRECORDSENTRY']._serialized_end=1749
  _globals['_SENDRESPONSE']._serialized_start=1751
  _globals['_SENDRESPONSE']._serialized_end=1873
  _globals['_SENDTOROUTEREQUEST']._serialized_start=1875
  _globals['_SENDTOROUTEREQUEST']._serialized_end=1985
  _globals['_CHANNELACCEPTREQUEST']._serialized_start=1988
  _globals['_CHANNELACCEPTREQUEST']._serialized_end=2396
  _globals['_CHANNELACCEPTRESPONSE']._serialized_start=2399
  _globals['_CHANNELACCEPTRESPONSE']._serialized_end=2662
  _globals['_CHANNELPOINT']._serialized_start=2664
  _globals['_CHANNELPOINT']._serialized_end=2774
  _globals['_OUTPOINT']._serialized_start=2776
  _globals['_OUTPOINT']._serialized_end=2846
  _globals['_PREVIOUSOUTPOINT']._serialized_start=2848
  _globals['_PREVIOUSOUTPOINT']._serialized_end=2907
  _globals['_LIGHTNINGADDRESS']._serialized_start=2909
  _globals['_LIGHTNINGADDRESS']._serialized_end=2957
  _globals['_ESTIMATEFEEREQUEST']._serialized_start=2960
  _globals['_ESTIMATEFEEREQUEST']._serialized_end=3167
  _globals['_ESTIMATEFEEREQUEST_ADDRTOAMOUNTENTRY']._serialized_start=3116
  _globals['_ESTIMATEFEEREQUEST_ADDRTOAMOUNTENTRY']._serialized_end=3167
  _globals['_ESTIMATEFEERESPONSE']._serialized_start=3169
  _globals['_ESTIMATEFEERESPONSE']._serialized_end=3264
  _globals['_SENDMANYREQUEST']._serialized_start=3267
  _globals['_SENDMANYREQUEST']._serialized_end=3532
  _globals['_SENDMANYREQUEST_ADDRTOAMOUNTENTRY']._serialized_start=3116
  _globals['_SENDMANYREQUEST_ADDRTOAMOUNTENTRY']._serialized_end=3167
  _globals['_SENDMANYRESPONSE']._serialized_start=3534
  _globals['_SENDMANYRESPONSE']._serialized_end=3566
  _globals['_SENDCOINSREQUEST']._serialized_start=3569
  _globals['_SENDCOINSREQUEST']._serialized_end=3766
  _globals['_SENDCOINSRESPONSE']._serialized_start=3768
  _globals['_SENDCOINSRESPONSE']._serialized_end=3801
  _globals['_LISTUNSPENTREQUEST']._serialized_start=3803
  _globals['_LISTUNSPENTREQUEST']._serialized_end=3878
  _globals['_LISTUNSPENTRESPONSE']._serialized_start=3880
  _globals['_LISTUNSPENTRESPONSE']._serialized_end=3929
  _globals['_NEWADDRESSREQUEST']._serialized_start=3931
  _globals['_NEWADDRESSREQUEST']._serialized_end=4001
  _globals['_NEWADDRESSRESPONSE']._serialized_start=4003
  _globals['_NEWADDRESSRESPONSE']._serialized_end=4040
  _globals['_SIGNMESSAGEREQUEST']._serialized_start=4042
  _globals['_SIGNMESSAGEREQUEST']._serialized_end=4096
  _globals['_SIGNMESSAGERESPONSE']._serialized_start=4098
  _globals['_SIGNMESSAGERESPONSE']._serialized_end=4138
  _globals['_VERIFYMESSAGEREQUEST']._serialized_start=4140
  _globals['_VERIFYMESSAGEREQUEST']._serialized_end=4194
  _globals['_VERIFYMESSAGERESPONSE']._serialized_start=4196
  _globals['_VERIFYMESSAGERESPONSE']._serialized_end=4250
  _globals['_CONNECTPEERREQUEST']._serialized_start=4252
  _globals['_CONNECTPEERREQUEST']._serialized_end=4342
  _globals['_CONNECTPEERRESPONSE']._serialized_start=4344
  _globals['_CONNECTPEERRESPONSE']._serialized_end=4365
  _globals['_DISCONNECTPEERREQUEST']._serialized_start=4367
  _globals['_DISCONNECTPEERREQUEST']._serialized_end=4407
  _globals['_DISCONNECTPEERRESPONSE']._serialized_start=4409
  _globals['_DISCONNECTPEERRESPONSE']._serialized_end=4433
  _globals['_HTLC']._serialized_start=4436
  _globals['_HTLC']._serialized_end=4601
  _globals['_CHANNELCONSTRAINTS']._serialized_start=4604
  _globals['_CHANNELCONSTRAINTS']._serialized_end=4774
  _globals['_CHANNEL']._serialized_start=4777
  _globals['_CHANNEL']._serialized_end=5730
  _globals['_LISTCHANNELSREQUEST']._serialized_start=5733
  _globals['_LISTCHANNELSREQUEST']._serialized_end=5882
  _globals['_LISTCHANNELSRESPONSE']._serialized_start=5884
  _globals['_LISTCHANNELSRESPONSE']._serialized_end=5940
  _globals['_ALIASMAP']._serialized_start=5942
  _globals['_ALIASMAP']._serialized_end=5988
  _globals['_LISTALIASESREQUEST']._serialized_start=5990
  _globals['_LISTALIASESREQUEST']._serialized_end=6010
  _globals['_LISTALIASESRESPONSE']._serialized_start=6012
  _globals['_LISTALIASESRESPONSE']._serialized_end=6070
  _globals['_CHANNELCLOSESUMMARY']._serialized_start=6073
  _globals['_CHANNELCLOSESUMMARY']._serialized_end=6685
  _globals['_CHANNELCLOSESUMMARY_CLOSURETYPE']._serialized_start=6547
  _globals['_CHANNELCLOSESUMMARY_CLOSURETYPE']._serialized_end=6685
  _globals['_RESOLUTION']._serialized_start=6688
  _globals['_RESOLUTION']._serialized_end=6866
  _globals['_CLOSEDCHANNELSREQUEST']._serialized_start=6869
  _globals['_CLOSEDCHANNELSREQUEST']._serialized_end=7017
  _globals['_CLOSEDCHANNELSRESPONSE']._serialized_start=7019
  _globals['_CLOSEDCHANNELSRESPONSE']._serialized_end=7089
  _globals['_PEER']._serialized_start=7092
  _globals['_PEER']._serialized_end=7587
  _globals['_PEER_FEATURESENTRY']._serialized_start=7442
  _globals['_PEER_FEATURESENTRY']._serialized_end=7505
  _globals['_PEER_SYNCTYPE']._serialized_start=7507
  _globals['_PEER_SYNCTYPE']._serialized_end=7587
  _globals['_TIMESTAMPEDERROR']._serialized_start=7589
  _globals['_TIMESTAMPEDERROR']._serialized_end=7641
  _globals['_LISTPEERSREQUEST']._serialized_start=7643
  _globals['_LISTPEERSREQUEST']._serialized_end=7683
  _globals['_LISTPEERSRESPONSE']._serialized_start=7685
  _globals['_LISTPEERSRESPONSE']._serialized_end=7732
  _globals['_PEEREVENTSUBSCRIPTION']._serialized_start=7734
  _globals['_PEEREVENTSUBSCRIPTION']._serialized_end=7757
  _globals['_PEEREVENT']._serialized_start=7759
  _globals['_PEEREVENT']._serialized_end=7877
  _globals['_PEEREVENT_EVENTTYPE']._serialized_start=7831
  _globals['_PEEREVENT_EVENTTYPE']._serialized_end=7877
  _globals['_GETINFOREQUEST']._serialized_start=7879
  _globals['_GETINFOREQUEST']._serialized_end=7895
  _globals['_GETINFORESPONSE']._serialized_start=7898
  _globals['_GETINFORESPONSE']._serialized_end=8508
  _globals['_GETINFORESPONSE_FEATURESENTRY']._serialized_start=7442
  _globals['_GETINFORESPONSE_FEATURESENTRY']._serialized_end=7505
  _globals['_GETRECOVERYINFOREQUEST']._serialized_start=8510
  _globals['_GETRECOVERYINFOREQUEST']._serialized_end=8534
  _globals['_GETRECOVERYINFORESPONSE']._serialized_start=8536
  _globals['_GETRECOVERYINFORESPONSE']._serialized_end=8629
  _globals['_CHAIN']._serialized_start=8631
  _globals['_CHAIN']._serialized_end=8674
  _globals['_CONFIRMATIONUPDATE']._serialized_start=8676
  _globals['_CONFIRMATIONUPDATE']._serialized_end=8761
  _globals['_CHANNELOPENUPDATE']._serialized_start=8763
  _globals['_CHANNELOPENUPDATE']._serialized_end=8826
  _globals['_CHANNELCLOSEUPDATE']._serialized_start=8828
  _globals['_CHANNELCLOSEUPDATE']._serialized_end=8887
  _globals['_CLOSECHANNELREQUEST']._serialized_start=8890
  _globals['_CLOSECHANNELREQUEST']._serialized_end=9093
  _globals['_CLOSESTATUSUPDATE']._serialized_start=9095
  _globals['_CLOSESTATUSUPDATE']._serialized_end=9220
  _globals['_PENDINGUPDATE']._serialized_start=9222
  _globals['_PENDINGUPDATE']._serialized_end=9273
  _globals['_READYFORPSBTFUNDING']._serialized_start=9275
  _globals['_READYFORPSBTFUNDING']._serialized_end=9359
  _globals['_BATCHOPENCHANNELREQUEST']._serialized_start=9362
  _globals['_BATCHOPENCHANNELREQUEST']._serialized_end=9535
  _globals['_BATCHOPENCHANNEL']._serialized_start=9538
  _globals['_BATCHOPENCHANNEL']._serialized_end=10043
  _globals['_BATCHOPENCHANNELRESPONSE']._serialized_start=10045
  _globals['_BATCHOPENCHANNELRESPONSE']._serialized_end=10119
  _globals['_OPENCHANNELREQUEST']._serialized_start=10122
  _globals['_OPENCHANNELREQUEST']._serialized_end=10848
  _globals['_OPENSTATUSUPDATE']._serialized_start=10851
  _globals['_OPENSTATUSUPDATE']._serialized_end=11046
  _globals['_KEYLOCATOR']._serialized_start=11048
  _globals['_KEYLOCATOR']._serialized_end=11099
  _globals['_KEYDESCRIPTOR']._serialized_start=11101
  _globals['_KEYDESCRIPTOR']._serialized_end=11175
  _globals['_CHANPOINTSHIM']._serialized_start=11178
  _globals['_CHANPOINTSHIM']._serialized_end=11370
  _globals['_PSBTSHIM']._serialized_start=11372
  _globals['_PSBTSHIM']._serialized_end=11446
  _globals['_FUNDINGSHIM']._serialized_start=11448
  _globals['_FUNDINGSHIM']._serialized_end=11556
  _globals['_FUNDINGSHIMCANCEL']._serialized_start=11558
  _globals['_FUNDINGSHIMCANCEL']._serialized_end=11602
  _globals['_FUNDINGPSBTVERIFY']._serialized_start=11604
  _globals['_FUNDINGPSBTVERIFY']._serialized_end=11692
  _globals['_FUNDINGPSBTFINALIZE']._serialized_start=11694
  _globals['_FUNDINGPSBTFINALIZE']._serialized_end=11783
  _globals['_FUNDINGTRANSITIONMSG']._serialized_start=11786
  _globals['_FUNDINGTRANSITIONMSG']._serialized_end=12015
  _globals['_FUNDINGSTATESTEPRESP']._serialized_start=12017
  _globals['_FUNDINGSTATESTEPRESP']._serialized_end=12039
  _globals['_PENDINGHTLC']._serialized_start=12042
  _globals['_PENDINGHTLC']._serialized_end=12176
  _globals['_PENDINGCHANNELSREQUEST']._serialized_start=12178
  _globals['_PENDINGCHANNELSREQUEST']._serialized_end=12202
  _globals['_PENDINGCHANNELSRESPONSE']._serialized_start=12205
  _globals['_PENDINGCHANNELSRESPONSE']._serialized_end=14033
  _globals['_PENDINGCHANNELSRESPONSE_PENDINGCHANNEL']._serialized_start=12603
  _globals['_PENDINGCHANNELSRESPONSE_PENDINGCHANNEL']._serialized_end=12973
  _globals['_PENDINGCHANNELSRESPONSE_PENDINGOPENCHANNEL']._serialized_start=12976
  _globals['_PENDINGCHANNELSRESPONSE_PENDINGOPENCHANNEL']._serialized_end=13160
  _globals['_PENDINGCHANNELSRESPONSE_WAITINGCLOSECHANNEL']._serialized_start=13163
  _globals['_PENDINGCHANNELSRESPONSE_WAITINGCLOSECHANNEL']._serialized_end=13358
  _globals['_PENDINGCHANNELSRESPONSE_COMMITMENTS']._serialized_start=13361
  _globals['_PENDINGCHANNELSRESPONSE_COMMITMENTS']._serialized_end=13544
  _globals['_PENDINGCHANNELSRESPONSE_CLOSEDCHANNEL']._serialized_start=13546
  _globals['_PENDINGCHANNELSRESPONSE_CLOSEDCHANNEL']._serialized_end=13647
  _globals['_PENDINGCHANNELSRESPONSE_FORCECLOSEDCHANNEL']._serialized_start=13650
  _globals['_PENDINGCHANNELSRESPONSE_FORCECLOSEDCHANNEL']._serialized_end=14033
  _globals['_PENDINGCHANNELSRESPONSE_FORCECLOSEDCHANNEL_ANCHORSTATE']._serialized_start=13984
  _globals['_PENDINGCHANNELSRESPONSE_FORCECLOSEDCHANNEL_ANCHORSTATE']._serialized_end=14033
  _globals['_CHANNELEVENTSUBSCRIPTION']._serialized_start=14035
  _globals['_CHANNELEVENTSUBSCRIPTION']._serialized_end=14061
  _globals['_CHANNELEVENTUPDATE']._serialized_start=14064
  _globals['_CHANNELEVENTUPDATE']._serialized_end=14595
  _globals['_CHANNELEVENTUPDATE_UPDATETYPE']._serialized_start=14438
  _globals['_CHANNELEVENTUPDATE_UPDATETYPE']._serialized_end=14584
  _globals['_WALLETACCOUNTBALANCE']._serialized_start=14597
  _globals['_WALLETACCOUNTBALANCE']._serialized_end=14675
  _globals['_WALLETBALANCEREQUEST']._serialized_start=14677
  _globals['_WALLETBALANCEREQUEST']._serialized_end=14716
  _globals['_WALLETBALANCERESPONSE']._serialized_start=14719
  _globals['_WALLETBALANCERESPONSE']._serialized_end=15042
  _globals['_WALLETBALANCERESPONSE_ACCOUNTBALANCEENTRY']._serialized_start=14960
  _globals['_WALLETBALANCERESPONSE_ACCOUNTBALANCEENTRY']._serialized_end=15042
  _globals['_AMOUNT']._serialized_start=15044
  _globals['_AMOUNT']._serialized_end=15079
  _globals['_CHANNELBALANCEREQUEST']._serialized_start=15081
  _globals['_CHANNELBALANCEREQUEST']._serialized_end=15104
  _globals['_CHANNELBALANCERESPONSE']._serialized_start=15107
  _globals['_CHANNELBALANCERESPONSE']._serialized_end=15463
  _globals['_QUERYROUTESREQUEST']._serialized_start=15466
  _globals['_QUERYROUTESREQUEST']._serialized_end=16135
  _globals['_QUERYROUTESREQUEST_DESTCUSTOMRECORDSENTRY']._serialized_start=1693
  _globals['_QUERYROUTESREQUEST_DESTCUSTOMRECORDSENTRY']._serialized_end=1749
  _globals['_NODEPAIR']._serialized_start=16137
  _globals['_NODEPAIR']._serialized_end=16173
  _globals['_EDGELOCATOR']._serialized_start=16175
  _globals['_EDGELOCATOR']._serialized_end=16239
  _globals['_QUERYROUTESRESPONSE']._serialized_start=16241
  _globals['_QUERYROUTESRESPONSE']._serialized_end=16314
  _globals['_HOP']._serialized_start=16317
  _globals['_HOP']._serialized_end=16795
  _globals['_HOP_CUSTOMRECORDSENTRY']._serialized_start=16743
  _globals['_HOP_CUSTOMRECORDSENTRY']._serialized_end=16795
  _globals['_MPPRECORD']._serialized_start=16797
  _globals['_MPPRECORD']._serialized_end=16854
  _globals['_AMPRECORD']._serialized_start=16856
  _globals['_AMPRECORD']._serialized_end=16924
  _globals['_ROUTE']._serialized_start=16927
  _globals['_ROUTE']._serialized_end=17081
  _globals['_NODEINFOREQUEST']._serialized_start=17083
  _globals['_NODEINFOREQUEST']._serialized_end=17143
  _globals['_NODEINFO']._serialized_start=17146
  _globals['_NODEINFO']._serialized_end=17276
  _globals['_LIGHTNINGNODE']._serialized_start=17279
  _globals['_LIGHTNINGNODE']._serialized_end=17639
  _globals['_LIGHTNINGNODE_FEATURESENTRY']._serialized_start=7442
  _globals['_LIGHTNINGNODE_FEATURESENTRY']._serialized_end=7505
  _globals['_LIGHTNINGNODE_CUSTOMRECORDSENTRY']._serialized_start=16743
  _globals['_LIGHTNINGNODE_CUSTOMRECORDSENTRY']._serialized_end=16795
  _globals['_NODEADDRESS']._serialized_start=17641
  _globals['_NODEADDRESS']._serialized_end=17685
  _globals['_ROUTINGPOLICY']._serialized_start=17688
  _globals['_ROUTINGPOLICY']._serialized_end=17979
  _globals['_ROUTINGPOLICY_CUSTOMRECORDSENTRY']._serialized_start=16743
  _globals['_ROUTINGPOLICY_CUSTOMRECORDSENTRY']._serialized_end=16795
  _globals['_CHANNELEDGE']._serialized_start=17982
  _globals['_CHANNELEDGE']._serialized_end=18325
  _globals['_CHANNELEDGE_CUSTOMRECORDSENTRY']._serialized_start=16743
  _globals['_CHANNELEDGE_CUSTOMRECORDSENTRY']._serialized_end=16795
  _globals['_CHANNELGRAPHREQUEST']._serialized_start=18327
  _globals['_CHANNELGRAPHREQUEST']._serialized_end=18377
  _globals['_CHANNELGRAPH']._serialized_start=18379
  _globals['_CHANNELGRAPH']._serialized_end=18465
  _globals['_NODEMETRICSREQUEST']._serialized_start=18467
  _globals['_NODEMETRICSREQUEST']._serialized_end=18525
  _globals['_NODEMETRICSRESPONSE']._serialized_start=18528
  _globals['_NODEMETRICSRESPONSE']._serialized_end=18718
  _globals['_NODEMETRICSRESPONSE_BETWEENNESSCENTRALITYENTRY']._serialized_start=18638
  _globals['_NODEMETRICSRESPONSE_BETWEENNESSCENTRALITYENTRY']._serialized_end=18718
  _globals['_FLOATMETRIC']._serialized_start=18720
  _globals['_FLOATMETRIC']._serialized_end=18774
  _globals['_CHANINFOREQUEST']._serialized_start=18776
  _globals['_CHANINFOREQUEST']._serialized_end=18814
  _globals['_NETWORKINFOREQUEST']._serialized_start=18816
  _globals['_NETWORKINFOREQUEST']._serialized_end=18836
  _globals['_NETWORKINFO']._serialized_start=18839
  _globals['_NETWORKINFO']._serialized_end=19134
  _globals['_STOPREQUEST']._serialized_start=19136
  _globals['_STOPREQUEST']._serialized_end=19149
  _globals['_STOPRESPONSE']._serialized_start=19151
  _globals['_STOPRESPONSE']._serialized_end=19165
  _globals['_GRAPHTOPOLOGYSUBSCRIPTION']._serialized_start=19167
  _globals['_GRAPHTOPOLOGYSUBSCRIPTION']._serialized_end=19194
  _globals['_GRAPHTOPOLOGYUPDATE']._serialized_start=19197
  _globals['_GRAPHTOPOLOGYUPDATE']._serialized_end=19360
  _globals['_NODEUPDATE']._serialized_start=19363
  _globals['_NODEUPDATE']._serialized_end=19639
  _globals['_NODEUPDATE_FEATURESENTRY']._serialized_start=7442
  _globals['_NODEUPDATE_FEATURESENTRY']._serialized_end=7505
  _globals['_CHANNELEDGEUPDATE']._serialized_start=19642
  _globals['_CHANNELEDGEUPDATE']._serialized_end=19838
  _globals['_CLOSEDCHANNELUPDATE']._serialized_start=19840
  _globals['_CLOSEDCHANNELUPDATE']._serialized_end=19964
  _globals['_HOPHINT']._serialized_start=19967
  _globals['_HOPHINT']._serialized_end=20101
  _globals['_SETID']._serialized_start=20103
  _globals['_SETID']._serialized_end=20126
  _globals['_ROUTEHINT']._serialized_start=20128
  _globals['_ROUTEHINT']._serialized_end=20174
  _globals['_BLINDEDPAYMENTPATH']._serialized_start=20177
  _globals['_BLINDEDPAYMENTPATH']._serialized_end=20402
  _globals['_BLINDEDPATH']._serialized_start=20404
  _globals['_BLINDEDPATH']._serialized_end=20509
  _globals['_BLINDEDHOP']._serialized_start=20511
  _globals['_BLINDEDHOP']._serialized_end=20569
  _globals['_AMPINVOICESTATE']._serialized_start=20571
  _globals['_AMPINVOICESTATE']._serialized_end=20694
  _globals['_INVOICE']._serialized_start=20697
  _globals['_INVOICE']._serialized_end=21598
  _globals['_INVOICE_FEATURESENTRY']._serialized_start=7442
  _globals['_INVOICE_FEATURESENTRY']._serialized_end=7505
  _globals['_INVOICE_AMPINVOICESTATEENTRY']._serialized_start=21447
  _globals['_INVOICE_AMPINVOICESTATEENTRY']._serialized_end=21525
  _globals['_INVOICE_INVOICESTATE']._serialized_start=21527
  _globals['_INVOICE_INVOICESTATE']._serialized_end=21592
  _globals['_INVOICEHTLC']._serialized_start=21601
  _globals['_INVOICEHTLC']._serialized_end=21972
  _globals['_INVOICEHTLC_CUSTOMRECORDSENTRY']._serialized_start=16743
  _globals['_INVOICEHTLC_CUSTOMRECORDSENTRY']._serialized_end=16795
  _globals['_AMP']._serialized_start=21974
  _globals['_AMP']._serialized_end=22068
  _globals['_ADDINVOICERESPONSE']._serialized_start=22070
  _globals['_ADDINVOICERESPONSE']._serialized_end=22172
  _globals['_PAYMENTHASH']._serialized_start=22174
  _globals['_PAYMENTHASH']._serialized_end=22227
  _globals['_LISTINVOICEREQUEST']._serialized_start=22230
  _globals['_LISTINVOICEREQUEST']._serialized_end=22394
  _globals['_LISTINVOICERESPONSE']._serialized_start=22396
  _globals['_LISTINVOICERESPONSE']._serialized_end=22506
  _globals['_INVOICESUBSCRIPTION']._serialized_start=22508
  _globals['_INVOICESUBSCRIPTION']._serialized_end=22570
  _globals['_PAYMENT']._serialized_start=22573
  _globals['_PAYMENT']._serialized_end=23072
  _globals['_PAYMENT_PAYMENTSTATUS']._serialized_start=22977
  _globals['_PAYMENT_PAYMENTSTATUS']._serialized_end=23066
  _globals['_HTLCATTEMPT']._serialized_start=23075
  _globals['_HTLCATTEMPT']._serialized_end=23341
  _globals['_HTLCATTEMPT_HTLCSTATUS']._serialized_start=23287
  _globals['_HTLCATTEMPT_HTLCSTATUS']._serialized_end=23341
  _globals['_LISTPAYMENTSREQUEST']._serialized_start=23344
  _globals['_LISTPAYMENTSREQUEST']._serialized_end=23541
  _globals['_LISTPAYMENTSRESPONSE']._serialized_start=23544
  _globals['_LISTPAYMENTSRESPONSE']._serialized_end=23683
  _globals['_DELETEPAYMENTREQUEST']._serialized_start=23685
  _globals['_DELETEPAYMENTREQUEST']._serialized_end=23756
  _globals['_DELETEALLPAYMENTSREQUEST']._serialized_start=23758
  _globals['_DELETEALLPAYMENTSREQUEST']._serialized_end=23841
  _globals['_DELETEPAYMENTRESPONSE']._serialized_start=23843
  _globals['_DELETEPAYMENTRESPONSE']._serialized_end=23866
  _globals['_DELETEALLPAYMENTSRESPONSE']._serialized_start=23868
  _globals['_DELETEALLPAYMENTSRESPONSE']._serialized_end=23895
  _globals['_ABANDONCHANNELREQUEST']._serialized_start=23898
  _globals['_ABANDONCHANNELREQUEST']._serialized_end=24032
  _globals['_ABANDONCHANNELRESPONSE']._serialized_start=24034
  _globals['_ABANDONCHANNELRESPONSE']._serialized_end=24058
  _globals['_DEBUGLEVELREQUEST']._serialized_start=24060
  _globals['_DEBUGLEVELREQUEST']._serialized_end=24113
  _globals['_DEBUGLEVELRESPONSE']._serialized_start=24115
  _globals['_DEBUGLEVELRESPONSE']._serialized_end=24156
  _globals['_PAYREQSTRING']._serialized_start=24158
  _globals['_PAYREQSTRING']._serialized_end=24189
  _globals['_PAYREQ']._serialized_start=24192
  _globals['_PAYREQ']._serialized_end=24582
  _globals['_PAYREQ_FEATURESENTRY']._serialized_start=7442
  _globals['_PAYREQ_FEATURESENTRY']._serialized_end=7505
  _globals['_FEATURE']._serialized_start=24584
  _globals['_FEATURE']._serialized_end=24646
  _globals['_FEEREPORTREQUEST']._serialized_start=24648
  _globals['_FEEREPORTREQUEST']._serialized_end=24666
  _globals['_CHANNELFEEREPORT']._serialized_start=24668
  _globals['_CHANNELFEEREPORT']._serialized_end=24792
  _globals['_FEEREPORTRESPONSE']._serialized_start=24795
  _globals['_FEEREPORTRESPONSE']._serialized_end=24927
  _globals['_POLICYUPDATEREQUEST']._serialized_start=24930
  _globals['_POLICYUPDATEREQUEST']._serialized_end=25188
  _globals['_FAILEDUPDATE']._serialized_start=25190
  _globals['_FAILEDUPDATE']._serialized_end=25299
  _globals['_POLICYUPDATERESPONSE']._serialized_start=25301
  _globals['_POLICYUPDATERESPONSE']._serialized_end=25368
  _globals['_FORWARDINGHISTORYREQUEST']._serialized_start=25371
  _globals['_FORWARDINGHISTORYREQUEST']._serialized_end=25508
  _globals['_FORWARDINGEVENT']._serialized_start=25511
  _globals['_FORWARDINGEVENT']._serialized_end=25776
  _globals['_FORWARDINGHISTORYRESPONSE']._serialized_start=25778
  _globals['_FORWARDINGHISTORYRESPONSE']._serialized_end=25883
  _globals['_EXPORTCHANNELBACKUPREQUEST']._serialized_start=25885
  _globals['_EXPORTCHANNELBACKUPREQUEST']._serialized_end=25954
  _globals['_CHANNELBACKUP']._serialized_start=25956
  _globals['_CHANNELBACKUP']._serialized_end=26033
  _globals['_MULTICHANBACKUP']._serialized_start=26035
  _globals['_MULTICHANBACKUP']._serialized_end=26121
  _globals['_CHANBACKUPEXPORTREQUEST']._serialized_start=26123
  _globals['_CHANBACKUPEXPORTREQUEST']._serialized_end=26148
  _globals['_CHANBACKUPSNAPSHOT']._serialized_start=26150
  _globals['_CHANBACKUPSNAPSHOT']._serialized_end=26273
  _globals['_CHANNELBACKUPS']._serialized_start=26275
  _globals['_CHANNELBACKUPS']._serialized_end=26335
  _globals['_RESTORECHANBACKUPREQUEST']._serialized_start=26337
  _globals['_RESTORECHANBACKUPREQUEST']._serialized_end=26449
  _globals['_RESTOREBACKUPRESPONSE']._serialized_start=26451
  _globals['_RESTOREBACKUPRESPONSE']._serialized_end=26474
  _globals['_CHANNELBACKUPSUBSCRIPTION']._serialized_start=26476
  _globals['_CHANNELBACKUPSUBSCRIPTION']._serialized_end=26503
  _globals['_VERIFYCHANBACKUPRESPONSE']._serialized_start=26505
  _globals['_VERIFYCHANBACKUPRESPONSE']._serialized_end=26531
  _globals['_MACAROONPERMISSION']._serialized_start=26533
  _globals['_MACAROONPERMISSION']._serialized_end=26585
  _globals['_BAKEMACAROONREQUEST']._serialized_start=26587
  _globals['_BAKEMACAROONREQUEST']._serialized_end=26713
  _globals['_BAKEMACAROONRESPONSE']._serialized_start=26715
  _globals['_BAKEMACAROONRESPONSE']._serialized_end=26755
  _globals['_LISTMACAROONIDSREQUEST']._serialized_start=26757
  _globals['_LISTMACAROONIDSREQUEST']._serialized_end=26781
  _globals['_LISTMACAROONIDSRESPONSE']._serialized_start=26783
  _globals['_LISTMACAROONIDSRESPONSE']._serialized_end=26830
  _globals['_DELETEMACAROONIDREQUEST']._serialized_start=26832
  _globals['_DELETEMACAROONIDREQUEST']._serialized_end=26878
  _globals['_DELETEMACAROONIDRESPONSE']._serialized_start=26880
  _globals['_DELETEMACAROONIDRESPONSE']._serialized_end=26923
  _globals['_MACAROONPERMISSIONLIST']._serialized_start=26925
  _globals['_MACAROONPERMISSIONLIST']._serialized_end=26997
  _globals['_LISTPERMISSIONSREQUEST']._serialized_start=26999
  _globals['_LISTPERMISSIONSREQUEST']._serialized_end=27023
  _globals['_LISTPERMISSIONSRESPONSE']._serialized_start=27026
  _globals['_LISTPERMISSIONSRESPONSE']._serialized_end=27223
  _globals['_LISTPERMISSIONSRESPONSE_METHODPERMISSIONSENTRY']._serialized_start=27136
  _globals['_LISTPERMISSIONSRESPONSE_METHODPERMISSIONSENTRY']._serialized_end=27223
  _globals['_FAILURE']._serialized_start=27226
  _globals['_FAILURE']._serialized_end=28207
  _globals['_FAILURE_FAILURECODE']._serialized_start=27450
  _globals['_FAILURE_FAILURECODE']._serialized_end=28201
  _globals['_CHANNELUPDATE']._serialized_start=28210
  _globals['_CHANNELUPDATE']._serialized_end=28492
  _globals['_MACAROONID']._serialized_start=28494
  _globals['_MACAROONID']._serialized_end=28564
  _globals['_OP']._serialized_start=28566
  _globals['_OP']._serialized_end=28603
  _globals['_CHECKMACPERMREQUEST']._serialized_start=28605
  _globals['_CHECKMACPERMREQUEST']._serialized_end=28712
  _globals['_CHECKMACPERMRESPONSE']._serialized_start=28714
  _globals['_CHECKMACPERMRESPONSE']._serialized_end=28751
  _globals['_RPCMIDDLEWAREREQUEST']._serialized_start=28754
  _globals['_RPCMIDDLEWAREREQUEST']._serialized_end=29028
  _globals['_STREAMAUTH']._serialized_start=29030
  _globals['_STREAMAUTH']._serialized_end=29067
  _globals['_RPCMESSAGE']._serialized_start=29069
  _globals['_RPCMESSAGE']._serialized_end=29183
  _globals['_RPCMIDDLEWARERESPONSE']._serialized_start=29186
  _globals['_RPCMIDDLEWARERESPONSE']._serialized_end=29348
  _globals['_MIDDLEWAREREGISTRATION']._serialized_start=29350
  _globals['_MIDDLEWAREREGISTRATION']._serialized_end=29460
  _globals['_INTERCEPTFEEDBACK']._serialized_start=29462
  _globals['_INTERCEPTFEEDBACK']._serialized_end=29554
  _globals['_LIGHTNING']._serialized_start=31630
  _globals['_LIGHTNING']._serialized_end=36606
# @@protoc_insertion_point(module_scope)
