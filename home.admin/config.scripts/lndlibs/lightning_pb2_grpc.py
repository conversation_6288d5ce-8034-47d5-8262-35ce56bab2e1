# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
from __future__ import absolute_import
import grpc

from . import lightning_pb2 as lightning__pb2

class LightningStub(object):
    """
    Comments in this file will be directly parsed into the API
    Documentation as descriptions of the associated method, message, or field.
    These descriptions should go right above the definition of the object, and
    can be in either block or // comment format.

    An RPC method can be matched to an lncli command by placing a line in the
    beginning of the description in exactly the following format:
    lncli: `methodname`

    Failure to specify the exact name of the command will cause documentation
    generation to fail.

    More information on how exactly the gRPC documentation is generated from
    this proto file can be found here:
    https://github.com/lightninglabs/lightning-api

    Lightning is the main RPC server of the daemon.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.WalletBalance = channel.unary_unary(
                '/lnrpc.Lightning/WalletBalance',
                request_serializer=lightning__pb2.WalletBalanceRequest.SerializeToString,
                response_deserializer=lightning__pb2.WalletBalanceResponse.FromString,
                )
        self.ChannelBalance = channel.unary_unary(
                '/lnrpc.Lightning/ChannelBalance',
                request_serializer=lightning__pb2.ChannelBalanceRequest.SerializeToString,
                response_deserializer=lightning__pb2.ChannelBalanceResponse.FromString,
                )
        self.GetTransactions = channel.unary_unary(
                '/lnrpc.Lightning/GetTransactions',
                request_serializer=lightning__pb2.GetTransactionsRequest.SerializeToString,
                response_deserializer=lightning__pb2.TransactionDetails.FromString,
                )
        self.EstimateFee = channel.unary_unary(
                '/lnrpc.Lightning/EstimateFee',
                request_serializer=lightning__pb2.EstimateFeeRequest.SerializeToString,
                response_deserializer=lightning__pb2.EstimateFeeResponse.FromString,
                )
        self.SendCoins = channel.unary_unary(
                '/lnrpc.Lightning/SendCoins',
                request_serializer=lightning__pb2.SendCoinsRequest.SerializeToString,
                response_deserializer=lightning__pb2.SendCoinsResponse.FromString,
                )
        self.ListUnspent = channel.unary_unary(
                '/lnrpc.Lightning/ListUnspent',
                request_serializer=lightning__pb2.ListUnspentRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListUnspentResponse.FromString,
                )
        self.SubscribeTransactions = channel.unary_stream(
                '/lnrpc.Lightning/SubscribeTransactions',
                request_serializer=lightning__pb2.GetTransactionsRequest.SerializeToString,
                response_deserializer=lightning__pb2.Transaction.FromString,
                )
        self.SendMany = channel.unary_unary(
                '/lnrpc.Lightning/SendMany',
                request_serializer=lightning__pb2.SendManyRequest.SerializeToString,
                response_deserializer=lightning__pb2.SendManyResponse.FromString,
                )
        self.NewAddress = channel.unary_unary(
                '/lnrpc.Lightning/NewAddress',
                request_serializer=lightning__pb2.NewAddressRequest.SerializeToString,
                response_deserializer=lightning__pb2.NewAddressResponse.FromString,
                )
        self.SignMessage = channel.unary_unary(
                '/lnrpc.Lightning/SignMessage',
                request_serializer=lightning__pb2.SignMessageRequest.SerializeToString,
                response_deserializer=lightning__pb2.SignMessageResponse.FromString,
                )
        self.VerifyMessage = channel.unary_unary(
                '/lnrpc.Lightning/VerifyMessage',
                request_serializer=lightning__pb2.VerifyMessageRequest.SerializeToString,
                response_deserializer=lightning__pb2.VerifyMessageResponse.FromString,
                )
        self.ConnectPeer = channel.unary_unary(
                '/lnrpc.Lightning/ConnectPeer',
                request_serializer=lightning__pb2.ConnectPeerRequest.SerializeToString,
                response_deserializer=lightning__pb2.ConnectPeerResponse.FromString,
                )
        self.DisconnectPeer = channel.unary_unary(
                '/lnrpc.Lightning/DisconnectPeer',
                request_serializer=lightning__pb2.DisconnectPeerRequest.SerializeToString,
                response_deserializer=lightning__pb2.DisconnectPeerResponse.FromString,
                )
        self.ListPeers = channel.unary_unary(
                '/lnrpc.Lightning/ListPeers',
                request_serializer=lightning__pb2.ListPeersRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListPeersResponse.FromString,
                )
        self.SubscribePeerEvents = channel.unary_stream(
                '/lnrpc.Lightning/SubscribePeerEvents',
                request_serializer=lightning__pb2.PeerEventSubscription.SerializeToString,
                response_deserializer=lightning__pb2.PeerEvent.FromString,
                )
        self.GetInfo = channel.unary_unary(
                '/lnrpc.Lightning/GetInfo',
                request_serializer=lightning__pb2.GetInfoRequest.SerializeToString,
                response_deserializer=lightning__pb2.GetInfoResponse.FromString,
                )
        self.GetRecoveryInfo = channel.unary_unary(
                '/lnrpc.Lightning/GetRecoveryInfo',
                request_serializer=lightning__pb2.GetRecoveryInfoRequest.SerializeToString,
                response_deserializer=lightning__pb2.GetRecoveryInfoResponse.FromString,
                )
        self.PendingChannels = channel.unary_unary(
                '/lnrpc.Lightning/PendingChannels',
                request_serializer=lightning__pb2.PendingChannelsRequest.SerializeToString,
                response_deserializer=lightning__pb2.PendingChannelsResponse.FromString,
                )
        self.ListChannels = channel.unary_unary(
                '/lnrpc.Lightning/ListChannels',
                request_serializer=lightning__pb2.ListChannelsRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListChannelsResponse.FromString,
                )
        self.SubscribeChannelEvents = channel.unary_stream(
                '/lnrpc.Lightning/SubscribeChannelEvents',
                request_serializer=lightning__pb2.ChannelEventSubscription.SerializeToString,
                response_deserializer=lightning__pb2.ChannelEventUpdate.FromString,
                )
        self.ClosedChannels = channel.unary_unary(
                '/lnrpc.Lightning/ClosedChannels',
                request_serializer=lightning__pb2.ClosedChannelsRequest.SerializeToString,
                response_deserializer=lightning__pb2.ClosedChannelsResponse.FromString,
                )
        self.OpenChannelSync = channel.unary_unary(
                '/lnrpc.Lightning/OpenChannelSync',
                request_serializer=lightning__pb2.OpenChannelRequest.SerializeToString,
                response_deserializer=lightning__pb2.ChannelPoint.FromString,
                )
        self.OpenChannel = channel.unary_stream(
                '/lnrpc.Lightning/OpenChannel',
                request_serializer=lightning__pb2.OpenChannelRequest.SerializeToString,
                response_deserializer=lightning__pb2.OpenStatusUpdate.FromString,
                )
        self.BatchOpenChannel = channel.unary_unary(
                '/lnrpc.Lightning/BatchOpenChannel',
                request_serializer=lightning__pb2.BatchOpenChannelRequest.SerializeToString,
                response_deserializer=lightning__pb2.BatchOpenChannelResponse.FromString,
                )
        self.FundingStateStep = channel.unary_unary(
                '/lnrpc.Lightning/FundingStateStep',
                request_serializer=lightning__pb2.FundingTransitionMsg.SerializeToString,
                response_deserializer=lightning__pb2.FundingStateStepResp.FromString,
                )
        self.ChannelAcceptor = channel.stream_stream(
                '/lnrpc.Lightning/ChannelAcceptor',
                request_serializer=lightning__pb2.ChannelAcceptResponse.SerializeToString,
                response_deserializer=lightning__pb2.ChannelAcceptRequest.FromString,
                )
        self.CloseChannel = channel.unary_stream(
                '/lnrpc.Lightning/CloseChannel',
                request_serializer=lightning__pb2.CloseChannelRequest.SerializeToString,
                response_deserializer=lightning__pb2.CloseStatusUpdate.FromString,
                )
        self.AbandonChannel = channel.unary_unary(
                '/lnrpc.Lightning/AbandonChannel',
                request_serializer=lightning__pb2.AbandonChannelRequest.SerializeToString,
                response_deserializer=lightning__pb2.AbandonChannelResponse.FromString,
                )
        self.SendPayment = channel.stream_stream(
                '/lnrpc.Lightning/SendPayment',
                request_serializer=lightning__pb2.SendRequest.SerializeToString,
                response_deserializer=lightning__pb2.SendResponse.FromString,
                )
        self.SendPaymentSync = channel.unary_unary(
                '/lnrpc.Lightning/SendPaymentSync',
                request_serializer=lightning__pb2.SendRequest.SerializeToString,
                response_deserializer=lightning__pb2.SendResponse.FromString,
                )
        self.SendToRoute = channel.stream_stream(
                '/lnrpc.Lightning/SendToRoute',
                request_serializer=lightning__pb2.SendToRouteRequest.SerializeToString,
                response_deserializer=lightning__pb2.SendResponse.FromString,
                )
        self.SendToRouteSync = channel.unary_unary(
                '/lnrpc.Lightning/SendToRouteSync',
                request_serializer=lightning__pb2.SendToRouteRequest.SerializeToString,
                response_deserializer=lightning__pb2.SendResponse.FromString,
                )
        self.AddInvoice = channel.unary_unary(
                '/lnrpc.Lightning/AddInvoice',
                request_serializer=lightning__pb2.Invoice.SerializeToString,
                response_deserializer=lightning__pb2.AddInvoiceResponse.FromString,
                )
        self.ListInvoices = channel.unary_unary(
                '/lnrpc.Lightning/ListInvoices',
                request_serializer=lightning__pb2.ListInvoiceRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListInvoiceResponse.FromString,
                )
        self.LookupInvoice = channel.unary_unary(
                '/lnrpc.Lightning/LookupInvoice',
                request_serializer=lightning__pb2.PaymentHash.SerializeToString,
                response_deserializer=lightning__pb2.Invoice.FromString,
                )
        self.SubscribeInvoices = channel.unary_stream(
                '/lnrpc.Lightning/SubscribeInvoices',
                request_serializer=lightning__pb2.InvoiceSubscription.SerializeToString,
                response_deserializer=lightning__pb2.Invoice.FromString,
                )
        self.DecodePayReq = channel.unary_unary(
                '/lnrpc.Lightning/DecodePayReq',
                request_serializer=lightning__pb2.PayReqString.SerializeToString,
                response_deserializer=lightning__pb2.PayReq.FromString,
                )
        self.ListPayments = channel.unary_unary(
                '/lnrpc.Lightning/ListPayments',
                request_serializer=lightning__pb2.ListPaymentsRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListPaymentsResponse.FromString,
                )
        self.DeletePayment = channel.unary_unary(
                '/lnrpc.Lightning/DeletePayment',
                request_serializer=lightning__pb2.DeletePaymentRequest.SerializeToString,
                response_deserializer=lightning__pb2.DeletePaymentResponse.FromString,
                )
        self.DeleteAllPayments = channel.unary_unary(
                '/lnrpc.Lightning/DeleteAllPayments',
                request_serializer=lightning__pb2.DeleteAllPaymentsRequest.SerializeToString,
                response_deserializer=lightning__pb2.DeleteAllPaymentsResponse.FromString,
                )
        self.DescribeGraph = channel.unary_unary(
                '/lnrpc.Lightning/DescribeGraph',
                request_serializer=lightning__pb2.ChannelGraphRequest.SerializeToString,
                response_deserializer=lightning__pb2.ChannelGraph.FromString,
                )
        self.GetNodeMetrics = channel.unary_unary(
                '/lnrpc.Lightning/GetNodeMetrics',
                request_serializer=lightning__pb2.NodeMetricsRequest.SerializeToString,
                response_deserializer=lightning__pb2.NodeMetricsResponse.FromString,
                )
        self.GetChanInfo = channel.unary_unary(
                '/lnrpc.Lightning/GetChanInfo',
                request_serializer=lightning__pb2.ChanInfoRequest.SerializeToString,
                response_deserializer=lightning__pb2.ChannelEdge.FromString,
                )
        self.GetNodeInfo = channel.unary_unary(
                '/lnrpc.Lightning/GetNodeInfo',
                request_serializer=lightning__pb2.NodeInfoRequest.SerializeToString,
                response_deserializer=lightning__pb2.NodeInfo.FromString,
                )
        self.QueryRoutes = channel.unary_unary(
                '/lnrpc.Lightning/QueryRoutes',
                request_serializer=lightning__pb2.QueryRoutesRequest.SerializeToString,
                response_deserializer=lightning__pb2.QueryRoutesResponse.FromString,
                )
        self.GetNetworkInfo = channel.unary_unary(
                '/lnrpc.Lightning/GetNetworkInfo',
                request_serializer=lightning__pb2.NetworkInfoRequest.SerializeToString,
                response_deserializer=lightning__pb2.NetworkInfo.FromString,
                )
        self.StopDaemon = channel.unary_unary(
                '/lnrpc.Lightning/StopDaemon',
                request_serializer=lightning__pb2.StopRequest.SerializeToString,
                response_deserializer=lightning__pb2.StopResponse.FromString,
                )
        self.SubscribeChannelGraph = channel.unary_stream(
                '/lnrpc.Lightning/SubscribeChannelGraph',
                request_serializer=lightning__pb2.GraphTopologySubscription.SerializeToString,
                response_deserializer=lightning__pb2.GraphTopologyUpdate.FromString,
                )
        self.DebugLevel = channel.unary_unary(
                '/lnrpc.Lightning/DebugLevel',
                request_serializer=lightning__pb2.DebugLevelRequest.SerializeToString,
                response_deserializer=lightning__pb2.DebugLevelResponse.FromString,
                )
        self.FeeReport = channel.unary_unary(
                '/lnrpc.Lightning/FeeReport',
                request_serializer=lightning__pb2.FeeReportRequest.SerializeToString,
                response_deserializer=lightning__pb2.FeeReportResponse.FromString,
                )
        self.UpdateChannelPolicy = channel.unary_unary(
                '/lnrpc.Lightning/UpdateChannelPolicy',
                request_serializer=lightning__pb2.PolicyUpdateRequest.SerializeToString,
                response_deserializer=lightning__pb2.PolicyUpdateResponse.FromString,
                )
        self.ForwardingHistory = channel.unary_unary(
                '/lnrpc.Lightning/ForwardingHistory',
                request_serializer=lightning__pb2.ForwardingHistoryRequest.SerializeToString,
                response_deserializer=lightning__pb2.ForwardingHistoryResponse.FromString,
                )
        self.ExportChannelBackup = channel.unary_unary(
                '/lnrpc.Lightning/ExportChannelBackup',
                request_serializer=lightning__pb2.ExportChannelBackupRequest.SerializeToString,
                response_deserializer=lightning__pb2.ChannelBackup.FromString,
                )
        self.ExportAllChannelBackups = channel.unary_unary(
                '/lnrpc.Lightning/ExportAllChannelBackups',
                request_serializer=lightning__pb2.ChanBackupExportRequest.SerializeToString,
                response_deserializer=lightning__pb2.ChanBackupSnapshot.FromString,
                )
        self.VerifyChanBackup = channel.unary_unary(
                '/lnrpc.Lightning/VerifyChanBackup',
                request_serializer=lightning__pb2.ChanBackupSnapshot.SerializeToString,
                response_deserializer=lightning__pb2.VerifyChanBackupResponse.FromString,
                )
        self.RestoreChannelBackups = channel.unary_unary(
                '/lnrpc.Lightning/RestoreChannelBackups',
                request_serializer=lightning__pb2.RestoreChanBackupRequest.SerializeToString,
                response_deserializer=lightning__pb2.RestoreBackupResponse.FromString,
                )
        self.SubscribeChannelBackups = channel.unary_stream(
                '/lnrpc.Lightning/SubscribeChannelBackups',
                request_serializer=lightning__pb2.ChannelBackupSubscription.SerializeToString,
                response_deserializer=lightning__pb2.ChanBackupSnapshot.FromString,
                )
        self.BakeMacaroon = channel.unary_unary(
                '/lnrpc.Lightning/BakeMacaroon',
                request_serializer=lightning__pb2.BakeMacaroonRequest.SerializeToString,
                response_deserializer=lightning__pb2.BakeMacaroonResponse.FromString,
                )
        self.ListMacaroonIDs = channel.unary_unary(
                '/lnrpc.Lightning/ListMacaroonIDs',
                request_serializer=lightning__pb2.ListMacaroonIDsRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListMacaroonIDsResponse.FromString,
                )
        self.DeleteMacaroonID = channel.unary_unary(
                '/lnrpc.Lightning/DeleteMacaroonID',
                request_serializer=lightning__pb2.DeleteMacaroonIDRequest.SerializeToString,
                response_deserializer=lightning__pb2.DeleteMacaroonIDResponse.FromString,
                )
        self.ListPermissions = channel.unary_unary(
                '/lnrpc.Lightning/ListPermissions',
                request_serializer=lightning__pb2.ListPermissionsRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListPermissionsResponse.FromString,
                )
        self.CheckMacaroonPermissions = channel.unary_unary(
                '/lnrpc.Lightning/CheckMacaroonPermissions',
                request_serializer=lightning__pb2.CheckMacPermRequest.SerializeToString,
                response_deserializer=lightning__pb2.CheckMacPermResponse.FromString,
                )
        self.RegisterRPCMiddleware = channel.stream_stream(
                '/lnrpc.Lightning/RegisterRPCMiddleware',
                request_serializer=lightning__pb2.RPCMiddlewareResponse.SerializeToString,
                response_deserializer=lightning__pb2.RPCMiddlewareRequest.FromString,
                )
        self.SendCustomMessage = channel.unary_unary(
                '/lnrpc.Lightning/SendCustomMessage',
                request_serializer=lightning__pb2.SendCustomMessageRequest.SerializeToString,
                response_deserializer=lightning__pb2.SendCustomMessageResponse.FromString,
                )
        self.SubscribeCustomMessages = channel.unary_stream(
                '/lnrpc.Lightning/SubscribeCustomMessages',
                request_serializer=lightning__pb2.SubscribeCustomMessagesRequest.SerializeToString,
                response_deserializer=lightning__pb2.CustomMessage.FromString,
                )
        self.ListAliases = channel.unary_unary(
                '/lnrpc.Lightning/ListAliases',
                request_serializer=lightning__pb2.ListAliasesRequest.SerializeToString,
                response_deserializer=lightning__pb2.ListAliasesResponse.FromString,
                )
        self.LookupHtlcResolution = channel.unary_unary(
                '/lnrpc.Lightning/LookupHtlcResolution',
                request_serializer=lightning__pb2.LookupHtlcResolutionRequest.SerializeToString,
                response_deserializer=lightning__pb2.LookupHtlcResolutionResponse.FromString,
                )


class LightningServicer(object):
    """
    Comments in this file will be directly parsed into the API
    Documentation as descriptions of the associated method, message, or field.
    These descriptions should go right above the definition of the object, and
    can be in either block or // comment format.

    An RPC method can be matched to an lncli command by placing a line in the
    beginning of the description in exactly the following format:
    lncli: `methodname`

    Failure to specify the exact name of the command will cause documentation
    generation to fail.

    More information on how exactly the gRPC documentation is generated from
    this proto file can be found here:
    https://github.com/lightninglabs/lightning-api

    Lightning is the main RPC server of the daemon.
    """

    def WalletBalance(self, request, context):
        """lncli: `walletbalance`
        WalletBalance returns total unspent outputs(confirmed and unconfirmed), all
        confirmed unspent outputs and all unconfirmed unspent outputs under control
        of the wallet.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChannelBalance(self, request, context):
        """lncli: `channelbalance`
        ChannelBalance returns a report on the total funds across all open channels,
        categorized in local/remote, pending local/remote and unsettled local/remote
        balances.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTransactions(self, request, context):
        """lncli: `listchaintxns`
        GetTransactions returns a list describing all the known transactions
        relevant to the wallet.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EstimateFee(self, request, context):
        """lncli: `estimatefee`
        EstimateFee asks the chain backend to estimate the fee rate and total fees
        for a transaction that pays to multiple specified outputs.

        When using REST, the `AddrToAmount` map type can be set by appending
        `&AddrToAmount[<address>]=<amount_to_send>` to the URL. Unfortunately this
        map type doesn't appear in the REST API documentation because of a bug in
        the grpc-gateway library.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendCoins(self, request, context):
        """lncli: `sendcoins`
        SendCoins executes a request to send coins to a particular address. Unlike
        SendMany, this RPC call only allows creating a single output at a time. If
        neither target_conf, or sat_per_vbyte are set, then the internal wallet will
        consult its fee model to determine a fee for the default confirmation
        target.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListUnspent(self, request, context):
        """lncli: `listunspent`
        Deprecated, use walletrpc.ListUnspent instead.

        ListUnspent returns a list of all utxos spendable by the wallet with a
        number of confirmations between the specified minimum and maximum.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeTransactions(self, request, context):
        """
        SubscribeTransactions creates a uni-directional stream from the server to
        the client in which any newly discovered transactions relevant to the
        wallet are sent over.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendMany(self, request, context):
        """lncli: `sendmany`
        SendMany handles a request for a transaction that creates multiple specified
        outputs in parallel. If neither target_conf, or sat_per_vbyte are set, then
        the internal wallet will consult its fee model to determine a fee for the
        default confirmation target.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def NewAddress(self, request, context):
        """lncli: `newaddress`
        NewAddress creates a new address under control of the local wallet.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SignMessage(self, request, context):
        """lncli: `signmessage`
        SignMessage signs a message with this node's private key. The returned
        signature string is `zbase32` encoded and pubkey recoverable, meaning that
        only the message digest and signature are needed for verification.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VerifyMessage(self, request, context):
        """lncli: `verifymessage`
        VerifyMessage verifies a signature over a message and recovers the signer's
        public key. The signature is only deemed valid if the recovered public key
        corresponds to a node key in the public Lightning network. The signature
        must be zbase32 encoded and signed by an active node in the resident node's
        channel database. In addition to returning the validity of the signature,
        VerifyMessage also returns the recovered pubkey from the signature.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ConnectPeer(self, request, context):
        """lncli: `connect`
        ConnectPeer attempts to establish a connection to a remote peer. This is at
        the networking level, and is used for communication between nodes. This is
        distinct from establishing a channel with a peer.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DisconnectPeer(self, request, context):
        """lncli: `disconnect`
        DisconnectPeer attempts to disconnect one peer from another identified by a
        given pubKey. In the case that we currently have a pending or active channel
        with the target peer, then this action will be not be allowed.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPeers(self, request, context):
        """lncli: `listpeers`
        ListPeers returns a verbose listing of all currently active peers.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribePeerEvents(self, request, context):
        """
        SubscribePeerEvents creates a uni-directional stream from the server to
        the client in which any events relevant to the state of peers are sent
        over. Events include peers going online and offline.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetInfo(self, request, context):
        """lncli: `getinfo`
        GetInfo returns general information concerning the lightning node including
        it's identity pubkey, alias, the chains it is connected to, and information
        concerning the number of open+pending channels.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRecoveryInfo(self, request, context):
        """* lncli: `getrecoveryinfo`
        GetRecoveryInfo returns information concerning the recovery mode including
        whether it's in a recovery mode, whether the recovery is finished, and the
        progress made so far.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PendingChannels(self, request, context):
        """TODO(roasbeef): merge with below with bool?

        lncli: `pendingchannels`
        PendingChannels returns a list of all the channels that are currently
        considered "pending". A channel is pending if it has finished the funding
        workflow and is waiting for confirmations for the funding txn, or is in the
        process of closure, either initiated cooperatively or non-cooperatively.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListChannels(self, request, context):
        """lncli: `listchannels`
        ListChannels returns a description of all the open channels that this node
        is a participant in.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeChannelEvents(self, request, context):
        """
        SubscribeChannelEvents creates a uni-directional stream from the server to
        the client in which any updates relevant to the state of the channels are
        sent over. Events include new active channels, inactive channels, and closed
        channels.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClosedChannels(self, request, context):
        """lncli: `closedchannels`
        ClosedChannels returns a description of all the closed channels that
        this node was a participant in.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenChannelSync(self, request, context):
        """
        OpenChannelSync is a synchronous version of the OpenChannel RPC call. This
        call is meant to be consumed by clients to the REST proxy. As with all
        other sync calls, all byte slices are intended to be populated as hex
        encoded strings.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenChannel(self, request, context):
        """lncli: `openchannel`
        OpenChannel attempts to open a singly funded channel specified in the
        request to a remote peer. Users are able to specify a target number of
        blocks that the funding transaction should be confirmed in, or a manual fee
        rate to us for the funding transaction. If neither are specified, then a
        lax block confirmation target is used. Each OpenStatusUpdate will return
        the pending channel ID of the in-progress channel. Depending on the
        arguments specified in the OpenChannelRequest, this pending channel ID can
        then be used to manually progress the channel funding flow.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchOpenChannel(self, request, context):
        """lncli: `batchopenchannel`
        BatchOpenChannel attempts to open multiple single-funded channels in a
        single transaction in an atomic way. This means either all channel open
        requests succeed at once or all attempts are aborted if any of them fail.
        This is the safer variant of using PSBTs to manually fund a batch of
        channels through the OpenChannel RPC.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FundingStateStep(self, request, context):
        """
        FundingStateStep is an advanced funding related call that allows the caller
        to either execute some preparatory steps for a funding workflow, or
        manually progress a funding workflow. The primary way a funding flow is
        identified is via its pending channel ID. As an example, this method can be
        used to specify that we're expecting a funding flow for a particular
        pending channel ID, for which we need to use specific parameters.
        Alternatively, this can be used to interactively drive PSBT signing for
        funding for partially complete funding transactions.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChannelAcceptor(self, request_iterator, context):
        """
        ChannelAcceptor dispatches a bi-directional streaming RPC in which
        OpenChannel requests are sent to the client and the client responds with
        a boolean that tells LND whether or not to accept the channel. This allows
        node operators to specify their own criteria for accepting inbound channels
        through a single persistent connection.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CloseChannel(self, request, context):
        """lncli: `closechannel`
        CloseChannel attempts to close an active channel identified by its channel
        outpoint (ChannelPoint). The actions of this method can additionally be
        augmented to attempt a force close after a timeout period in the case of an
        inactive peer. If a non-force close (cooperative closure) is requested,
        then the user can specify either a target number of blocks until the
        closure transaction is confirmed, or a manual fee rate. If neither are
        specified, then a default lax, block confirmation target is used.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AbandonChannel(self, request, context):
        """lncli: `abandonchannel`
        AbandonChannel removes all channel state from the database except for a
        close summary. This method can be used to get rid of permanently unusable
        channels due to bugs fixed in newer versions of lnd. This method can also be
        used to remove externally funded channels where the funding transaction was
        never broadcast. Only available for non-externally funded channels in dev
        build.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendPayment(self, request_iterator, context):
        """lncli: `sendpayment`
        Deprecated, use routerrpc.SendPaymentV2. SendPayment dispatches a
        bi-directional streaming RPC for sending payments through the Lightning
        Network. A single RPC invocation creates a persistent bi-directional
        stream allowing clients to rapidly send payments through the Lightning
        Network with a single persistent connection.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendPaymentSync(self, request, context):
        """
        SendPaymentSync is the synchronous non-streaming version of SendPayment.
        This RPC is intended to be consumed by clients of the REST proxy.
        Additionally, this RPC expects the destination's public key and the payment
        hash (if any) to be encoded as hex strings.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendToRoute(self, request_iterator, context):
        """lncli: `sendtoroute`
        Deprecated, use routerrpc.SendToRouteV2. SendToRoute is a bi-directional
        streaming RPC for sending payment through the Lightning Network. This
        method differs from SendPayment in that it allows users to specify a full
        route manually. This can be used for things like rebalancing, and atomic
        swaps.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendToRouteSync(self, request, context):
        """
        SendToRouteSync is a synchronous version of SendToRoute. It Will block
        until the payment either fails or succeeds.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddInvoice(self, request, context):
        """lncli: `addinvoice`
        AddInvoice attempts to add a new invoice to the invoice database. Any
        duplicated invoices are rejected, therefore all invoices *must* have a
        unique payment preimage.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListInvoices(self, request, context):
        """lncli: `listinvoices`
        ListInvoices returns a list of all the invoices currently stored within the
        database. Any active debug invoices are ignored. It has full support for
        paginated responses, allowing users to query for specific invoices through
        their add_index. This can be done by using either the first_index_offset or
        last_index_offset fields included in the response as the index_offset of the
        next request. By default, the first 100 invoices created will be returned.
        Backwards pagination is also supported through the Reversed flag.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LookupInvoice(self, request, context):
        """lncli: `lookupinvoice`
        LookupInvoice attempts to look up an invoice according to its payment hash.
        The passed payment hash *must* be exactly 32 bytes, if not, an error is
        returned.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeInvoices(self, request, context):
        """
        SubscribeInvoices returns a uni-directional stream (server -> client) for
        notifying the client of newly added/settled invoices. The caller can
        optionally specify the add_index and/or the settle_index. If the add_index
        is specified, then we'll first start by sending add invoice events for all
        invoices with an add_index greater than the specified value. If the
        settle_index is specified, the next, we'll send out all settle events for
        invoices with a settle_index greater than the specified value. One or both
        of these fields can be set. If no fields are set, then we'll only send out
        the latest add/settle events.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DecodePayReq(self, request, context):
        """lncli: `decodepayreq`
        DecodePayReq takes an encoded payment request string and attempts to decode
        it, returning a full description of the conditions encoded within the
        payment request.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPayments(self, request, context):
        """lncli: `listpayments`
        ListPayments returns a list of all outgoing payments.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePayment(self, request, context):
        """
        DeletePayment deletes an outgoing payment from DB. Note that it will not
        attempt to delete an In-Flight payment, since that would be unsafe.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAllPayments(self, request, context):
        """
        DeleteAllPayments deletes all outgoing payments from DB. Note that it will
        not attempt to delete In-Flight payments, since that would be unsafe.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeGraph(self, request, context):
        """lncli: `describegraph`
        DescribeGraph returns a description of the latest graph state from the
        point of view of the node. The graph information is partitioned into two
        components: all the nodes/vertexes, and all the edges that connect the
        vertexes themselves. As this is a directed graph, the edges also contain
        the node directional specific routing policy which includes: the time lock
        delta, fee information, etc.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNodeMetrics(self, request, context):
        """lncli: `getnodemetrics`
        GetNodeMetrics returns node metrics calculated from the graph. Currently
        the only supported metric is betweenness centrality of individual nodes.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetChanInfo(self, request, context):
        """lncli: `getchaninfo`
        GetChanInfo returns the latest authenticated network announcement for the
        given channel identified by its channel ID: an 8-byte integer which
        uniquely identifies the location of transaction's funding output within the
        blockchain.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNodeInfo(self, request, context):
        """lncli: `getnodeinfo`
        GetNodeInfo returns the latest advertised, aggregated, and authenticated
        channel information for the specified node identified by its public key.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueryRoutes(self, request, context):
        """lncli: `queryroutes`
        QueryRoutes attempts to query the daemon's Channel Router for a possible
        route to a target destination capable of carrying a specific amount of
        satoshis. The returned route contains the full details required to craft and
        send an HTLC, also including the necessary information that should be
        present within the Sphinx packet encapsulated within the HTLC.

        When using REST, the `dest_custom_records` map type can be set by appending
        `&dest_custom_records[<record_number>]=<record_data_base64_url_encoded>`
        to the URL. Unfortunately this map type doesn't appear in the REST API
        documentation because of a bug in the grpc-gateway library.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNetworkInfo(self, request, context):
        """lncli: `getnetworkinfo`
        GetNetworkInfo returns some basic stats about the known channel graph from
        the point of view of the node.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopDaemon(self, request, context):
        """lncli: `stop`
        StopDaemon will send a shutdown request to the interrupt handler, triggering
        a graceful shutdown of the daemon.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeChannelGraph(self, request, context):
        """
        SubscribeChannelGraph launches a streaming RPC that allows the caller to
        receive notifications upon any changes to the channel graph topology from
        the point of view of the responding node. Events notified include: new
        nodes coming online, nodes updating their authenticated attributes, new
        channels being advertised, updates in the routing policy for a directional
        channel edge, and when channels are closed on-chain.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DebugLevel(self, request, context):
        """lncli: `debuglevel`
        DebugLevel allows a caller to programmatically set the logging verbosity of
        lnd. The logging can be targeted according to a coarse daemon-wide logging
        level, or in a granular fashion to specify the logging for a target
        sub-system.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FeeReport(self, request, context):
        """lncli: `feereport`
        FeeReport allows the caller to obtain a report detailing the current fee
        schedule enforced by the node globally for each channel.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateChannelPolicy(self, request, context):
        """lncli: `updatechanpolicy`
        UpdateChannelPolicy allows the caller to update the fee schedule and
        channel policies for all channels globally, or a particular channel.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ForwardingHistory(self, request, context):
        """lncli: `fwdinghistory`
        ForwardingHistory allows the caller to query the htlcswitch for a record of
        all HTLCs forwarded within the target time range, and integer offset
        within that time range, for a maximum number of events. If no maximum number
        of events is specified, up to 100 events will be returned. If no time-range
        is specified, then events will be returned in the order that they occured.

        A list of forwarding events are returned. The size of each forwarding event
        is 40 bytes, and the max message size able to be returned in gRPC is 4 MiB.
        As a result each message can only contain 50k entries. Each response has
        the index offset of the last entry. The index offset can be provided to the
        request to allow the caller to skip a series of records.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ExportChannelBackup(self, request, context):
        """lncli: `exportchanbackup`
        ExportChannelBackup attempts to return an encrypted static channel backup
        for the target channel identified by it channel point. The backup is
        encrypted with a key generated from the aezeed seed of the user. The
        returned backup can either be restored using the RestoreChannelBackup
        method once lnd is running, or via the InitWallet and UnlockWallet methods
        from the WalletUnlocker service.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ExportAllChannelBackups(self, request, context):
        """
        ExportAllChannelBackups returns static channel backups for all existing
        channels known to lnd. A set of regular singular static channel backups for
        each channel are returned. Additionally, a multi-channel backup is returned
        as well, which contains a single encrypted blob containing the backups of
        each channel.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VerifyChanBackup(self, request, context):
        """
        VerifyChanBackup allows a caller to verify the integrity of a channel backup
        snapshot. This method will accept either a packed Single or a packed Multi.
        Specifying both will result in an error.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestoreChannelBackups(self, request, context):
        """lncli: `restorechanbackup`
        RestoreChannelBackups accepts a set of singular channel backups, or a
        single encrypted multi-chan backup and attempts to recover any funds
        remaining within the channel. If we are able to unpack the backup, then the
        new channel will be shown under listchannels, as well as pending channels.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeChannelBackups(self, request, context):
        """
        SubscribeChannelBackups allows a client to sub-subscribe to the most up to
        date information concerning the state of all channel backups. Each time a
        new channel is added, we return the new set of channels, along with a
        multi-chan backup containing the backup info for all channels. Each time a
        channel is closed, we send a new update, which contains new new chan back
        ups, but the updated set of encrypted multi-chan backups with the closed
        channel(s) removed.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BakeMacaroon(self, request, context):
        """lncli: `bakemacaroon`
        BakeMacaroon allows the creation of a new macaroon with custom read and
        write permissions. No first-party caveats are added since this can be done
        offline.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListMacaroonIDs(self, request, context):
        """lncli: `listmacaroonids`
        ListMacaroonIDs returns all root key IDs that are in use.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteMacaroonID(self, request, context):
        """lncli: `deletemacaroonid`
        DeleteMacaroonID deletes the specified macaroon ID and invalidates all
        macaroons derived from that ID.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPermissions(self, request, context):
        """lncli: `listpermissions`
        ListPermissions lists all RPC method URIs and their required macaroon
        permissions to access them.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckMacaroonPermissions(self, request, context):
        """
        CheckMacaroonPermissions checks whether a request follows the constraints
        imposed on the macaroon and that the macaroon is authorized to follow the
        provided permissions.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RegisterRPCMiddleware(self, request_iterator, context):
        """
        RegisterRPCMiddleware adds a new gRPC middleware to the interceptor chain. A
        gRPC middleware is software component external to lnd that aims to add
        additional business logic to lnd by observing/intercepting/validating
        incoming gRPC client requests and (if needed) replacing/overwriting outgoing
        messages before they're sent to the client. When registering the middleware
        must identify itself and indicate what custom macaroon caveats it wants to
        be responsible for. Only requests that contain a macaroon with that specific
        custom caveat are then sent to the middleware for inspection. The other
        option is to register for the read-only mode in which all requests/responses
        are forwarded for interception to the middleware but the middleware is not
        allowed to modify any responses. As a security measure, _no_ middleware can
        modify responses for requests made with _unencumbered_ macaroons!
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendCustomMessage(self, request, context):
        """lncli: `sendcustom`
        SendCustomMessage sends a custom peer message.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeCustomMessages(self, request, context):
        """lncli: `subscribecustom`
        SubscribeCustomMessages subscribes to a stream of incoming custom peer
        messages.

        To include messages with type outside of the custom range (>= 32768) lnd
        needs to be compiled with  the `dev` build tag, and the message type to
        override should be specified in lnd's experimental protocol configuration.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAliases(self, request, context):
        """lncli: `listaliases`
        ListAliases returns the set of all aliases that have ever existed with
        their confirmed SCID (if it exists) and/or the base SCID (in the case of
        zero conf).
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LookupHtlcResolution(self, request, context):
        """
        LookupHtlcResolution retrieves a final htlc resolution from the database.
        If the htlc has no final resolution yet, a NotFound grpc status code is
        returned.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LightningServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'WalletBalance': grpc.unary_unary_rpc_method_handler(
                    servicer.WalletBalance,
                    request_deserializer=lightning__pb2.WalletBalanceRequest.FromString,
                    response_serializer=lightning__pb2.WalletBalanceResponse.SerializeToString,
            ),
            'ChannelBalance': grpc.unary_unary_rpc_method_handler(
                    servicer.ChannelBalance,
                    request_deserializer=lightning__pb2.ChannelBalanceRequest.FromString,
                    response_serializer=lightning__pb2.ChannelBalanceResponse.SerializeToString,
            ),
            'GetTransactions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTransactions,
                    request_deserializer=lightning__pb2.GetTransactionsRequest.FromString,
                    response_serializer=lightning__pb2.TransactionDetails.SerializeToString,
            ),
            'EstimateFee': grpc.unary_unary_rpc_method_handler(
                    servicer.EstimateFee,
                    request_deserializer=lightning__pb2.EstimateFeeRequest.FromString,
                    response_serializer=lightning__pb2.EstimateFeeResponse.SerializeToString,
            ),
            'SendCoins': grpc.unary_unary_rpc_method_handler(
                    servicer.SendCoins,
                    request_deserializer=lightning__pb2.SendCoinsRequest.FromString,
                    response_serializer=lightning__pb2.SendCoinsResponse.SerializeToString,
            ),
            'ListUnspent': grpc.unary_unary_rpc_method_handler(
                    servicer.ListUnspent,
                    request_deserializer=lightning__pb2.ListUnspentRequest.FromString,
                    response_serializer=lightning__pb2.ListUnspentResponse.SerializeToString,
            ),
            'SubscribeTransactions': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeTransactions,
                    request_deserializer=lightning__pb2.GetTransactionsRequest.FromString,
                    response_serializer=lightning__pb2.Transaction.SerializeToString,
            ),
            'SendMany': grpc.unary_unary_rpc_method_handler(
                    servicer.SendMany,
                    request_deserializer=lightning__pb2.SendManyRequest.FromString,
                    response_serializer=lightning__pb2.SendManyResponse.SerializeToString,
            ),
            'NewAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.NewAddress,
                    request_deserializer=lightning__pb2.NewAddressRequest.FromString,
                    response_serializer=lightning__pb2.NewAddressResponse.SerializeToString,
            ),
            'SignMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.SignMessage,
                    request_deserializer=lightning__pb2.SignMessageRequest.FromString,
                    response_serializer=lightning__pb2.SignMessageResponse.SerializeToString,
            ),
            'VerifyMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.VerifyMessage,
                    request_deserializer=lightning__pb2.VerifyMessageRequest.FromString,
                    response_serializer=lightning__pb2.VerifyMessageResponse.SerializeToString,
            ),
            'ConnectPeer': grpc.unary_unary_rpc_method_handler(
                    servicer.ConnectPeer,
                    request_deserializer=lightning__pb2.ConnectPeerRequest.FromString,
                    response_serializer=lightning__pb2.ConnectPeerResponse.SerializeToString,
            ),
            'DisconnectPeer': grpc.unary_unary_rpc_method_handler(
                    servicer.DisconnectPeer,
                    request_deserializer=lightning__pb2.DisconnectPeerRequest.FromString,
                    response_serializer=lightning__pb2.DisconnectPeerResponse.SerializeToString,
            ),
            'ListPeers': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPeers,
                    request_deserializer=lightning__pb2.ListPeersRequest.FromString,
                    response_serializer=lightning__pb2.ListPeersResponse.SerializeToString,
            ),
            'SubscribePeerEvents': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribePeerEvents,
                    request_deserializer=lightning__pb2.PeerEventSubscription.FromString,
                    response_serializer=lightning__pb2.PeerEvent.SerializeToString,
            ),
            'GetInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetInfo,
                    request_deserializer=lightning__pb2.GetInfoRequest.FromString,
                    response_serializer=lightning__pb2.GetInfoResponse.SerializeToString,
            ),
            'GetRecoveryInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRecoveryInfo,
                    request_deserializer=lightning__pb2.GetRecoveryInfoRequest.FromString,
                    response_serializer=lightning__pb2.GetRecoveryInfoResponse.SerializeToString,
            ),
            'PendingChannels': grpc.unary_unary_rpc_method_handler(
                    servicer.PendingChannels,
                    request_deserializer=lightning__pb2.PendingChannelsRequest.FromString,
                    response_serializer=lightning__pb2.PendingChannelsResponse.SerializeToString,
            ),
            'ListChannels': grpc.unary_unary_rpc_method_handler(
                    servicer.ListChannels,
                    request_deserializer=lightning__pb2.ListChannelsRequest.FromString,
                    response_serializer=lightning__pb2.ListChannelsResponse.SerializeToString,
            ),
            'SubscribeChannelEvents': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeChannelEvents,
                    request_deserializer=lightning__pb2.ChannelEventSubscription.FromString,
                    response_serializer=lightning__pb2.ChannelEventUpdate.SerializeToString,
            ),
            'ClosedChannels': grpc.unary_unary_rpc_method_handler(
                    servicer.ClosedChannels,
                    request_deserializer=lightning__pb2.ClosedChannelsRequest.FromString,
                    response_serializer=lightning__pb2.ClosedChannelsResponse.SerializeToString,
            ),
            'OpenChannelSync': grpc.unary_unary_rpc_method_handler(
                    servicer.OpenChannelSync,
                    request_deserializer=lightning__pb2.OpenChannelRequest.FromString,
                    response_serializer=lightning__pb2.ChannelPoint.SerializeToString,
            ),
            'OpenChannel': grpc.unary_stream_rpc_method_handler(
                    servicer.OpenChannel,
                    request_deserializer=lightning__pb2.OpenChannelRequest.FromString,
                    response_serializer=lightning__pb2.OpenStatusUpdate.SerializeToString,
            ),
            'BatchOpenChannel': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchOpenChannel,
                    request_deserializer=lightning__pb2.BatchOpenChannelRequest.FromString,
                    response_serializer=lightning__pb2.BatchOpenChannelResponse.SerializeToString,
            ),
            'FundingStateStep': grpc.unary_unary_rpc_method_handler(
                    servicer.FundingStateStep,
                    request_deserializer=lightning__pb2.FundingTransitionMsg.FromString,
                    response_serializer=lightning__pb2.FundingStateStepResp.SerializeToString,
            ),
            'ChannelAcceptor': grpc.stream_stream_rpc_method_handler(
                    servicer.ChannelAcceptor,
                    request_deserializer=lightning__pb2.ChannelAcceptResponse.FromString,
                    response_serializer=lightning__pb2.ChannelAcceptRequest.SerializeToString,
            ),
            'CloseChannel': grpc.unary_stream_rpc_method_handler(
                    servicer.CloseChannel,
                    request_deserializer=lightning__pb2.CloseChannelRequest.FromString,
                    response_serializer=lightning__pb2.CloseStatusUpdate.SerializeToString,
            ),
            'AbandonChannel': grpc.unary_unary_rpc_method_handler(
                    servicer.AbandonChannel,
                    request_deserializer=lightning__pb2.AbandonChannelRequest.FromString,
                    response_serializer=lightning__pb2.AbandonChannelResponse.SerializeToString,
            ),
            'SendPayment': grpc.stream_stream_rpc_method_handler(
                    servicer.SendPayment,
                    request_deserializer=lightning__pb2.SendRequest.FromString,
                    response_serializer=lightning__pb2.SendResponse.SerializeToString,
            ),
            'SendPaymentSync': grpc.unary_unary_rpc_method_handler(
                    servicer.SendPaymentSync,
                    request_deserializer=lightning__pb2.SendRequest.FromString,
                    response_serializer=lightning__pb2.SendResponse.SerializeToString,
            ),
            'SendToRoute': grpc.stream_stream_rpc_method_handler(
                    servicer.SendToRoute,
                    request_deserializer=lightning__pb2.SendToRouteRequest.FromString,
                    response_serializer=lightning__pb2.SendResponse.SerializeToString,
            ),
            'SendToRouteSync': grpc.unary_unary_rpc_method_handler(
                    servicer.SendToRouteSync,
                    request_deserializer=lightning__pb2.SendToRouteRequest.FromString,
                    response_serializer=lightning__pb2.SendResponse.SerializeToString,
            ),
            'AddInvoice': grpc.unary_unary_rpc_method_handler(
                    servicer.AddInvoice,
                    request_deserializer=lightning__pb2.Invoice.FromString,
                    response_serializer=lightning__pb2.AddInvoiceResponse.SerializeToString,
            ),
            'ListInvoices': grpc.unary_unary_rpc_method_handler(
                    servicer.ListInvoices,
                    request_deserializer=lightning__pb2.ListInvoiceRequest.FromString,
                    response_serializer=lightning__pb2.ListInvoiceResponse.SerializeToString,
            ),
            'LookupInvoice': grpc.unary_unary_rpc_method_handler(
                    servicer.LookupInvoice,
                    request_deserializer=lightning__pb2.PaymentHash.FromString,
                    response_serializer=lightning__pb2.Invoice.SerializeToString,
            ),
            'SubscribeInvoices': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeInvoices,
                    request_deserializer=lightning__pb2.InvoiceSubscription.FromString,
                    response_serializer=lightning__pb2.Invoice.SerializeToString,
            ),
            'DecodePayReq': grpc.unary_unary_rpc_method_handler(
                    servicer.DecodePayReq,
                    request_deserializer=lightning__pb2.PayReqString.FromString,
                    response_serializer=lightning__pb2.PayReq.SerializeToString,
            ),
            'ListPayments': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPayments,
                    request_deserializer=lightning__pb2.ListPaymentsRequest.FromString,
                    response_serializer=lightning__pb2.ListPaymentsResponse.SerializeToString,
            ),
            'DeletePayment': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePayment,
                    request_deserializer=lightning__pb2.DeletePaymentRequest.FromString,
                    response_serializer=lightning__pb2.DeletePaymentResponse.SerializeToString,
            ),
            'DeleteAllPayments': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAllPayments,
                    request_deserializer=lightning__pb2.DeleteAllPaymentsRequest.FromString,
                    response_serializer=lightning__pb2.DeleteAllPaymentsResponse.SerializeToString,
            ),
            'DescribeGraph': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeGraph,
                    request_deserializer=lightning__pb2.ChannelGraphRequest.FromString,
                    response_serializer=lightning__pb2.ChannelGraph.SerializeToString,
            ),
            'GetNodeMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNodeMetrics,
                    request_deserializer=lightning__pb2.NodeMetricsRequest.FromString,
                    response_serializer=lightning__pb2.NodeMetricsResponse.SerializeToString,
            ),
            'GetChanInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetChanInfo,
                    request_deserializer=lightning__pb2.ChanInfoRequest.FromString,
                    response_serializer=lightning__pb2.ChannelEdge.SerializeToString,
            ),
            'GetNodeInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNodeInfo,
                    request_deserializer=lightning__pb2.NodeInfoRequest.FromString,
                    response_serializer=lightning__pb2.NodeInfo.SerializeToString,
            ),
            'QueryRoutes': grpc.unary_unary_rpc_method_handler(
                    servicer.QueryRoutes,
                    request_deserializer=lightning__pb2.QueryRoutesRequest.FromString,
                    response_serializer=lightning__pb2.QueryRoutesResponse.SerializeToString,
            ),
            'GetNetworkInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNetworkInfo,
                    request_deserializer=lightning__pb2.NetworkInfoRequest.FromString,
                    response_serializer=lightning__pb2.NetworkInfo.SerializeToString,
            ),
            'StopDaemon': grpc.unary_unary_rpc_method_handler(
                    servicer.StopDaemon,
                    request_deserializer=lightning__pb2.StopRequest.FromString,
                    response_serializer=lightning__pb2.StopResponse.SerializeToString,
            ),
            'SubscribeChannelGraph': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeChannelGraph,
                    request_deserializer=lightning__pb2.GraphTopologySubscription.FromString,
                    response_serializer=lightning__pb2.GraphTopologyUpdate.SerializeToString,
            ),
            'DebugLevel': grpc.unary_unary_rpc_method_handler(
                    servicer.DebugLevel,
                    request_deserializer=lightning__pb2.DebugLevelRequest.FromString,
                    response_serializer=lightning__pb2.DebugLevelResponse.SerializeToString,
            ),
            'FeeReport': grpc.unary_unary_rpc_method_handler(
                    servicer.FeeReport,
                    request_deserializer=lightning__pb2.FeeReportRequest.FromString,
                    response_serializer=lightning__pb2.FeeReportResponse.SerializeToString,
            ),
            'UpdateChannelPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateChannelPolicy,
                    request_deserializer=lightning__pb2.PolicyUpdateRequest.FromString,
                    response_serializer=lightning__pb2.PolicyUpdateResponse.SerializeToString,
            ),
            'ForwardingHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.ForwardingHistory,
                    request_deserializer=lightning__pb2.ForwardingHistoryRequest.FromString,
                    response_serializer=lightning__pb2.ForwardingHistoryResponse.SerializeToString,
            ),
            'ExportChannelBackup': grpc.unary_unary_rpc_method_handler(
                    servicer.ExportChannelBackup,
                    request_deserializer=lightning__pb2.ExportChannelBackupRequest.FromString,
                    response_serializer=lightning__pb2.ChannelBackup.SerializeToString,
            ),
            'ExportAllChannelBackups': grpc.unary_unary_rpc_method_handler(
                    servicer.ExportAllChannelBackups,
                    request_deserializer=lightning__pb2.ChanBackupExportRequest.FromString,
                    response_serializer=lightning__pb2.ChanBackupSnapshot.SerializeToString,
            ),
            'VerifyChanBackup': grpc.unary_unary_rpc_method_handler(
                    servicer.VerifyChanBackup,
                    request_deserializer=lightning__pb2.ChanBackupSnapshot.FromString,
                    response_serializer=lightning__pb2.VerifyChanBackupResponse.SerializeToString,
            ),
            'RestoreChannelBackups': grpc.unary_unary_rpc_method_handler(
                    servicer.RestoreChannelBackups,
                    request_deserializer=lightning__pb2.RestoreChanBackupRequest.FromString,
                    response_serializer=lightning__pb2.RestoreBackupResponse.SerializeToString,
            ),
            'SubscribeChannelBackups': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeChannelBackups,
                    request_deserializer=lightning__pb2.ChannelBackupSubscription.FromString,
                    response_serializer=lightning__pb2.ChanBackupSnapshot.SerializeToString,
            ),
            'BakeMacaroon': grpc.unary_unary_rpc_method_handler(
                    servicer.BakeMacaroon,
                    request_deserializer=lightning__pb2.BakeMacaroonRequest.FromString,
                    response_serializer=lightning__pb2.BakeMacaroonResponse.SerializeToString,
            ),
            'ListMacaroonIDs': grpc.unary_unary_rpc_method_handler(
                    servicer.ListMacaroonIDs,
                    request_deserializer=lightning__pb2.ListMacaroonIDsRequest.FromString,
                    response_serializer=lightning__pb2.ListMacaroonIDsResponse.SerializeToString,
            ),
            'DeleteMacaroonID': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteMacaroonID,
                    request_deserializer=lightning__pb2.DeleteMacaroonIDRequest.FromString,
                    response_serializer=lightning__pb2.DeleteMacaroonIDResponse.SerializeToString,
            ),
            'ListPermissions': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPermissions,
                    request_deserializer=lightning__pb2.ListPermissionsRequest.FromString,
                    response_serializer=lightning__pb2.ListPermissionsResponse.SerializeToString,
            ),
            'CheckMacaroonPermissions': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckMacaroonPermissions,
                    request_deserializer=lightning__pb2.CheckMacPermRequest.FromString,
                    response_serializer=lightning__pb2.CheckMacPermResponse.SerializeToString,
            ),
            'RegisterRPCMiddleware': grpc.stream_stream_rpc_method_handler(
                    servicer.RegisterRPCMiddleware,
                    request_deserializer=lightning__pb2.RPCMiddlewareResponse.FromString,
                    response_serializer=lightning__pb2.RPCMiddlewareRequest.SerializeToString,
            ),
            'SendCustomMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.SendCustomMessage,
                    request_deserializer=lightning__pb2.SendCustomMessageRequest.FromString,
                    response_serializer=lightning__pb2.SendCustomMessageResponse.SerializeToString,
            ),
            'SubscribeCustomMessages': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeCustomMessages,
                    request_deserializer=lightning__pb2.SubscribeCustomMessagesRequest.FromString,
                    response_serializer=lightning__pb2.CustomMessage.SerializeToString,
            ),
            'ListAliases': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAliases,
                    request_deserializer=lightning__pb2.ListAliasesRequest.FromString,
                    response_serializer=lightning__pb2.ListAliasesResponse.SerializeToString,
            ),
            'LookupHtlcResolution': grpc.unary_unary_rpc_method_handler(
                    servicer.LookupHtlcResolution,
                    request_deserializer=lightning__pb2.LookupHtlcResolutionRequest.FromString,
                    response_serializer=lightning__pb2.LookupHtlcResolutionResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'lnrpc.Lightning', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Lightning(object):
    """
    Comments in this file will be directly parsed into the API
    Documentation as descriptions of the associated method, message, or field.
    These descriptions should go right above the definition of the object, and
    can be in either block or // comment format.

    An RPC method can be matched to an lncli command by placing a line in the
    beginning of the description in exactly the following format:
    lncli: `methodname`

    Failure to specify the exact name of the command will cause documentation
    generation to fail.

    More information on how exactly the gRPC documentation is generated from
    this proto file can be found here:
    https://github.com/lightninglabs/lightning-api

    Lightning is the main RPC server of the daemon.
    """

    @staticmethod
    def WalletBalance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/WalletBalance',
            lightning__pb2.WalletBalanceRequest.SerializeToString,
            lightning__pb2.WalletBalanceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ChannelBalance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ChannelBalance',
            lightning__pb2.ChannelBalanceRequest.SerializeToString,
            lightning__pb2.ChannelBalanceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTransactions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/GetTransactions',
            lightning__pb2.GetTransactionsRequest.SerializeToString,
            lightning__pb2.TransactionDetails.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EstimateFee(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/EstimateFee',
            lightning__pb2.EstimateFeeRequest.SerializeToString,
            lightning__pb2.EstimateFeeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendCoins(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/SendCoins',
            lightning__pb2.SendCoinsRequest.SerializeToString,
            lightning__pb2.SendCoinsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListUnspent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListUnspent',
            lightning__pb2.ListUnspentRequest.SerializeToString,
            lightning__pb2.ListUnspentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeTransactions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/SubscribeTransactions',
            lightning__pb2.GetTransactionsRequest.SerializeToString,
            lightning__pb2.Transaction.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendMany(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/SendMany',
            lightning__pb2.SendManyRequest.SerializeToString,
            lightning__pb2.SendManyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def NewAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/NewAddress',
            lightning__pb2.NewAddressRequest.SerializeToString,
            lightning__pb2.NewAddressResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SignMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/SignMessage',
            lightning__pb2.SignMessageRequest.SerializeToString,
            lightning__pb2.SignMessageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VerifyMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/VerifyMessage',
            lightning__pb2.VerifyMessageRequest.SerializeToString,
            lightning__pb2.VerifyMessageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ConnectPeer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ConnectPeer',
            lightning__pb2.ConnectPeerRequest.SerializeToString,
            lightning__pb2.ConnectPeerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DisconnectPeer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/DisconnectPeer',
            lightning__pb2.DisconnectPeerRequest.SerializeToString,
            lightning__pb2.DisconnectPeerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListPeers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListPeers',
            lightning__pb2.ListPeersRequest.SerializeToString,
            lightning__pb2.ListPeersResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribePeerEvents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/SubscribePeerEvents',
            lightning__pb2.PeerEventSubscription.SerializeToString,
            lightning__pb2.PeerEvent.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/GetInfo',
            lightning__pb2.GetInfoRequest.SerializeToString,
            lightning__pb2.GetInfoResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRecoveryInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/GetRecoveryInfo',
            lightning__pb2.GetRecoveryInfoRequest.SerializeToString,
            lightning__pb2.GetRecoveryInfoResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PendingChannels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/PendingChannels',
            lightning__pb2.PendingChannelsRequest.SerializeToString,
            lightning__pb2.PendingChannelsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListChannels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListChannels',
            lightning__pb2.ListChannelsRequest.SerializeToString,
            lightning__pb2.ListChannelsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeChannelEvents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/SubscribeChannelEvents',
            lightning__pb2.ChannelEventSubscription.SerializeToString,
            lightning__pb2.ChannelEventUpdate.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClosedChannels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ClosedChannels',
            lightning__pb2.ClosedChannelsRequest.SerializeToString,
            lightning__pb2.ClosedChannelsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OpenChannelSync(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/OpenChannelSync',
            lightning__pb2.OpenChannelRequest.SerializeToString,
            lightning__pb2.ChannelPoint.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OpenChannel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/OpenChannel',
            lightning__pb2.OpenChannelRequest.SerializeToString,
            lightning__pb2.OpenStatusUpdate.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def BatchOpenChannel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/BatchOpenChannel',
            lightning__pb2.BatchOpenChannelRequest.SerializeToString,
            lightning__pb2.BatchOpenChannelResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FundingStateStep(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/FundingStateStep',
            lightning__pb2.FundingTransitionMsg.SerializeToString,
            lightning__pb2.FundingStateStepResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ChannelAcceptor(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/lnrpc.Lightning/ChannelAcceptor',
            lightning__pb2.ChannelAcceptResponse.SerializeToString,
            lightning__pb2.ChannelAcceptRequest.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CloseChannel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/CloseChannel',
            lightning__pb2.CloseChannelRequest.SerializeToString,
            lightning__pb2.CloseStatusUpdate.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AbandonChannel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/AbandonChannel',
            lightning__pb2.AbandonChannelRequest.SerializeToString,
            lightning__pb2.AbandonChannelResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendPayment(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/lnrpc.Lightning/SendPayment',
            lightning__pb2.SendRequest.SerializeToString,
            lightning__pb2.SendResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendPaymentSync(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/SendPaymentSync',
            lightning__pb2.SendRequest.SerializeToString,
            lightning__pb2.SendResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendToRoute(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/lnrpc.Lightning/SendToRoute',
            lightning__pb2.SendToRouteRequest.SerializeToString,
            lightning__pb2.SendResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendToRouteSync(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/SendToRouteSync',
            lightning__pb2.SendToRouteRequest.SerializeToString,
            lightning__pb2.SendResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddInvoice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/AddInvoice',
            lightning__pb2.Invoice.SerializeToString,
            lightning__pb2.AddInvoiceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListInvoices(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListInvoices',
            lightning__pb2.ListInvoiceRequest.SerializeToString,
            lightning__pb2.ListInvoiceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LookupInvoice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/LookupInvoice',
            lightning__pb2.PaymentHash.SerializeToString,
            lightning__pb2.Invoice.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeInvoices(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/SubscribeInvoices',
            lightning__pb2.InvoiceSubscription.SerializeToString,
            lightning__pb2.Invoice.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DecodePayReq(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/DecodePayReq',
            lightning__pb2.PayReqString.SerializeToString,
            lightning__pb2.PayReq.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListPayments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListPayments',
            lightning__pb2.ListPaymentsRequest.SerializeToString,
            lightning__pb2.ListPaymentsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeletePayment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/DeletePayment',
            lightning__pb2.DeletePaymentRequest.SerializeToString,
            lightning__pb2.DeletePaymentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteAllPayments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/DeleteAllPayments',
            lightning__pb2.DeleteAllPaymentsRequest.SerializeToString,
            lightning__pb2.DeleteAllPaymentsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DescribeGraph(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/DescribeGraph',
            lightning__pb2.ChannelGraphRequest.SerializeToString,
            lightning__pb2.ChannelGraph.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNodeMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/GetNodeMetrics',
            lightning__pb2.NodeMetricsRequest.SerializeToString,
            lightning__pb2.NodeMetricsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetChanInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/GetChanInfo',
            lightning__pb2.ChanInfoRequest.SerializeToString,
            lightning__pb2.ChannelEdge.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNodeInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/GetNodeInfo',
            lightning__pb2.NodeInfoRequest.SerializeToString,
            lightning__pb2.NodeInfo.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueryRoutes(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/QueryRoutes',
            lightning__pb2.QueryRoutesRequest.SerializeToString,
            lightning__pb2.QueryRoutesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNetworkInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/GetNetworkInfo',
            lightning__pb2.NetworkInfoRequest.SerializeToString,
            lightning__pb2.NetworkInfo.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StopDaemon(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/StopDaemon',
            lightning__pb2.StopRequest.SerializeToString,
            lightning__pb2.StopResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeChannelGraph(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/SubscribeChannelGraph',
            lightning__pb2.GraphTopologySubscription.SerializeToString,
            lightning__pb2.GraphTopologyUpdate.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DebugLevel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/DebugLevel',
            lightning__pb2.DebugLevelRequest.SerializeToString,
            lightning__pb2.DebugLevelResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FeeReport(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/FeeReport',
            lightning__pb2.FeeReportRequest.SerializeToString,
            lightning__pb2.FeeReportResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateChannelPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/UpdateChannelPolicy',
            lightning__pb2.PolicyUpdateRequest.SerializeToString,
            lightning__pb2.PolicyUpdateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ForwardingHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ForwardingHistory',
            lightning__pb2.ForwardingHistoryRequest.SerializeToString,
            lightning__pb2.ForwardingHistoryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ExportChannelBackup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ExportChannelBackup',
            lightning__pb2.ExportChannelBackupRequest.SerializeToString,
            lightning__pb2.ChannelBackup.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ExportAllChannelBackups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ExportAllChannelBackups',
            lightning__pb2.ChanBackupExportRequest.SerializeToString,
            lightning__pb2.ChanBackupSnapshot.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VerifyChanBackup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/VerifyChanBackup',
            lightning__pb2.ChanBackupSnapshot.SerializeToString,
            lightning__pb2.VerifyChanBackupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RestoreChannelBackups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/RestoreChannelBackups',
            lightning__pb2.RestoreChanBackupRequest.SerializeToString,
            lightning__pb2.RestoreBackupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeChannelBackups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/SubscribeChannelBackups',
            lightning__pb2.ChannelBackupSubscription.SerializeToString,
            lightning__pb2.ChanBackupSnapshot.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def BakeMacaroon(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/BakeMacaroon',
            lightning__pb2.BakeMacaroonRequest.SerializeToString,
            lightning__pb2.BakeMacaroonResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListMacaroonIDs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListMacaroonIDs',
            lightning__pb2.ListMacaroonIDsRequest.SerializeToString,
            lightning__pb2.ListMacaroonIDsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteMacaroonID(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/DeleteMacaroonID',
            lightning__pb2.DeleteMacaroonIDRequest.SerializeToString,
            lightning__pb2.DeleteMacaroonIDResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListPermissions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListPermissions',
            lightning__pb2.ListPermissionsRequest.SerializeToString,
            lightning__pb2.ListPermissionsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CheckMacaroonPermissions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/CheckMacaroonPermissions',
            lightning__pb2.CheckMacPermRequest.SerializeToString,
            lightning__pb2.CheckMacPermResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RegisterRPCMiddleware(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/lnrpc.Lightning/RegisterRPCMiddleware',
            lightning__pb2.RPCMiddlewareResponse.SerializeToString,
            lightning__pb2.RPCMiddlewareRequest.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendCustomMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/SendCustomMessage',
            lightning__pb2.SendCustomMessageRequest.SerializeToString,
            lightning__pb2.SendCustomMessageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeCustomMessages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/lnrpc.Lightning/SubscribeCustomMessages',
            lightning__pb2.SubscribeCustomMessagesRequest.SerializeToString,
            lightning__pb2.CustomMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListAliases(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/ListAliases',
            lightning__pb2.ListAliasesRequest.SerializeToString,
            lightning__pb2.ListAliasesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LookupHtlcResolution(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/lnrpc.Lightning/LookupHtlcResolution',
            lightning__pb2.LookupHtlcResolutionRequest.SerializeToString,
            lightning__pb2.LookupHtlcResolutionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
