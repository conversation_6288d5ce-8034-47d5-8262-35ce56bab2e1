#!/bin/bash

# https://github.com/Start9Labs/c-lightning-http-plugin/commits/master
clHTTPpluginVersion="1dbb6537e0ec5fb9b8edde10db6b4cc613ccdb19"

# command info
if [ $# -lt 1 ] || [ "$1" = "-h" ] || [ "$1" = "-help" ];then
  echo
  echo "Install, remove, connect the c-lightning-http-plugin"
  echo "version: $clHTTPpluginVersion"
  echo "Implemented for mainnet only."
  echo "Usage:"
  echo "cl-plugin.http.sh [on|off|connect] <norestart>"
  echo
  exit 1
fi

PGPsigner="web-flow"
PGPpubkeyLink="https://github.com/${PGPsigner}.gpg"
PGPpubkeyFingerprint="(4AEE18F83AFDEB23|B5690EEEBB952194)"

# source <(/home/<USER>/config.scripts/network.aliases.sh getvars cl <mainnet|testnet|signet>)
source <(/home/<USER>/config.scripts/network.aliases.sh getvars cl mainnet)

if [ $1 = connect ];then
  toraddress=$(sudo cat /mnt/hdd/app-data/tor/clHTTPplugin/hostname)
  PASSWORD_B=$(sudo cat /mnt/hdd/app-data/bitcoin/bitcoin.conf | grep rpcpassword | cut -c 13-)
  # https://github.com/rootzoll/raspiblitz/issues/2579#issuecomment-936091256
  # http://rpcuser:<EMAIL>:9080
  url="http://lightning:${PASSWORD_B}@${toraddress}:9080"
  clear
  echo
  sudo /home/<USER>/config.scripts/blitz.display.sh qr "${toraddress}"
  echo "
Connect Fully Noded

In Fully Noded go to 'Settings' > 'Node Manager' > +, from there you will be automatically prompted to add a node:

    add a label
    add the rpc user: lightning
    add the rpc password is your Password_B
    add the onion address (also shown on the display as a QR and below), ensure you add the port at the end:
    ${toraddress}:9080"

    qrencode -t ANSIUTF8 "${toraddress}:9080"

    echo "
    ignore the macaroon and cert as that is for LND only

Thats it, Fully Noded will now automatically use those credentials for any lightning related functionality.
You can only have one lightning node at a a time, to add a new one just overwrite the existing credentials.

In Fully Noded you will see lightning bolt zap buttons in a few places, tap them to see what they do.

Find the most up-to-date version of this info at:
https://github.com/Fonta1n3/FullyNoded/blob/master/Docs/Lightning.md#connect-fully-noded
"
  echo
  echo "# Press enter to continue to show the full connection URL with all the info above"
  read key
  sudo /home/<USER>/config.scripts/blitz.display.sh hide
  sudo /home/<USER>/config.scripts/blitz.display.sh qr "${url}"
  clear
  echo "
Core Lightning connection URL code for Fully Noded:
The string shown is:
$url
"
  qrencode -t ANSIUTF8 "${url}"
  echo
  echo "# Press enter to hide the QRcode from the LCD"
  read key
  sudo /home/<USER>/config.scripts/blitz.display.sh hide
  exit 0
fi

if [ "$1" = "on" ];then

  echo
  echo "# Installing Rust for the bitcoin user"
  echo
  sudo -u bitcoin curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sudo -u bitcoin sh -s -- -y

  if [ ! -f /home/<USER>/cl-plugins-available/c-lightning-http-plugin ];then
    sudo -u bitcoin mkdir /home/<USER>/cl-plugins-available
    cd /home/<USER>/cl-plugins-available || exit 1
    sudo -u bitcoin git clone https://github.com/Start9Labs/c-lightning-http-plugin.git
    cd c-lightning-http-plugin || exit 1
    sudo -u bitcoin git reset --hard ${clHTTPpluginVersion} || exit 1

    sudo -u bitcoin /home/<USER>/config.scripts/blitz.git-verify.sh \
     "${PGPsigner}" "${PGPpubkeyLink}" "${PGPpubkeyFingerprint}" || exit 1

    echo
    echo "# change CL REST port to 9080"
    sudo sed -i "s/8080/9080/g" src/rpc.rs
    echo
    sudo -u bitcoin /home/<USER>/.cargo/bin/cargo build --release
    sudo chmod a+x /home/<USER>/cl-plugins-available/c-lightning-http-plugin/target/release/c-lightning-http-plugin

    # clean up
    sudo rm -R /home/<USER>/.cargo
    sudo rm -R /home/<USER>/.rustup
  fi

  if [ ! -L /home/<USER>/cl-plugins-enabled/c-lightning-http-plugin ];then
    sudo ln -s /home/<USER>/cl-plugins-available/c-lightning-http-plugin/target/release/c-lightning-http-plugin \
               /home/<USER>/cl-plugins-enabled
  fi

  ##########
  # Config #
  ##########
  if ! grep -Eq "^http-pass=" ${CLCONF};then
    echo "# Editing ${CLCONF}"
    echo "# See: https://github.com/Fonta1n3/FullyNoded/blob/master/Docs/Lightning.md#setup-c-lightning-http-plugin"
    PASSWORD_B=$(sudo cat /mnt/hdd/app-data/bitcoin/bitcoin.conf | grep rpcpassword | cut -c 13-)
    echo "
http-pass=${PASSWORD_B}
" | sudo tee -a ${CLCONF}

  else
    echo "# clHTTPplugin is already configured in ${CLCONF}"
  fi

  # hidden service to https://xx.onion
  /home/<USER>/config.scripts/tor.onion-service.sh clHTTPplugin 9080 9080

  # setting value in raspi blitz config
  /home/<USER>/config.scripts/blitz.conf.sh set clHTTPplugin "on"

  source <(/home/<USER>/_cache.sh get state)
  if [ "${state}" == "ready" ] && [ "$2" != "norestart" ]; then
    echo "# Restart the lightningd.service to activate clHTTPplugin"
    sudo systemctl restart lightningd
  fi

  echo "# clHTTPplugin was installed"
  echo "# Monitor with:"
  echo "sudo journalctl | grep clHTTPplugin | tail -n5"
  echo "sudo tail -n 100 -f /home/<USER>/.lightning/${CLNETWORK}/cl.log | grep clHTTPplugin"

fi

if [ "$1" = "off" ];then
  # delete symlink
  sudo rm -rf /home/<USER>/cl-plugins-enabled/c-lightning-http-plugin

  echo "# Editing ${CLCONF}"
  sudo sed -i "/^http-pass/d" ${CLCONF}

  echo "# Restart the lightningd.service to deactivate clHTTPplugin"
  sudo systemctl restart lightningd

  /home/<USER>/config.scripts/tor.onion-service.sh off clHTTPplugin

  # purge
  if [ "$(echo "$@" | grep -c purge)" -gt 0 ];then
    echo "# Delete plugin"
    sudo rm -rf /home/<USER>/cl-plugins-available/c-lightning-http-plugin
  fi
  # setting value in raspi blitz config
  /home/<USER>/config.scripts/blitz.conf.sh set clHTTPplugin "off"
  echo "# clHTTPplugin was uninstalled"

fi
