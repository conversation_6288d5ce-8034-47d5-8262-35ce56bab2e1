<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogShowQrCode</class>
 <widget class="QDialog" name="DialogShowQrCode">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>480</width>
    <height>320</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QDialogButtonBox" name="buttonBox">
   <property name="geometry">
    <rect>
     <x>326</x>
     <y>268</y>
     <width>150</width>
     <height>50</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>24</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: lightgrey;
font: 24pt &quot;Arial&quot;;</string>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <property name="standardButtons">
    <set>QDialogButtonBox::Ok</set>
   </property>
  </widget>
  <widget class="QLabel" name="top_right_logo">
   <property name="geometry">
    <rect>
     <x>430</x>
     <y>2</y>
     <width>40</width>
     <height>60</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="../resources.qrc">:/RaspiBlitz/images/RaspiBlitz_Logo_Berry.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QFrame" name="frame">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>320</width>
     <height>320</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(255, 255, 255);</string>
   </property>
   <property name="frameShape">
    <enum>QFrame::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Raised</enum>
   </property>
   <widget class="QLabel" name="qcode">
    <property name="geometry">
     <rect>
      <x>1</x>
      <y>1</y>
      <width>318</width>
      <height>318</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: white</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="pixmap">
     <pixmap resource="../resources.qrc">:/RaspiBlitz/images/RaspiBlitz_Logo_Stacked.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="margin">
     <number>16</number>
    </property>
   </widget>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>330</x>
     <y>4</y>
     <width>88</width>
     <height>60</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="../resources.qrc">:/RaspiBlitz/images/RaspiBlitz_Logo_Stacked.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>70</y>
     <width>161</width>
     <height>191</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>6</number>
    </property>
    <property name="rightMargin">
     <number>6</number>
    </property>
    <item>
     <widget class="Line" name="line">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="memo_key">
      <property name="font">
       <font>
        <family>Arial</family>
        <pointsize>11</pointsize>
        <weight>75</weight>
        <italic>false</italic>
        <bold>true</bold>
        <underline>false</underline>
       </font>
      </property>
      <property name="text">
       <string>Memo</string>
      </property>
      <property name="scaledContents">
       <bool>false</bool>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
      </property>
      <property name="wordWrap">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="memo_value">
      <property name="font">
       <font>
        <family>Arial</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>RB-Vivid-Badger</string>
      </property>
      <property name="scaledContents">
       <bool>false</bool>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
      </property>
      <property name="wordWrap">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="status_key">
        <property name="font">
         <font>
          <family>Arial</family>
          <pointsize>11</pointsize>
          <weight>75</weight>
          <bold>true</bold>
          <underline>false</underline>
         </font>
        </property>
        <property name="text">
         <string>Status</string>
        </property>
        <property name="scaledContents">
         <bool>false</bool>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="status_value">
        <property name="font">
         <font>
          <family>Arial</family>
          <pointsize>11</pointsize>
         </font>
        </property>
        <property name="text">
         <string>Open/Paid</string>
        </property>
        <property name="scaledContents">
         <bool>false</bool>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QLabel" name="inv_amt_key">
      <property name="font">
       <font>
        <family>Arial</family>
        <pointsize>11</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>Invoice Amount</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="inv_amt_value">
      <property name="font">
       <font>
        <family>Arial</family>
        <pointsize>11</pointsize>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="text">
       <string>123456798</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="amt_paid_key">
      <property name="font">
       <font>
        <family>Arial</family>
        <pointsize>11</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>Amount Paid</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="amt_paid_value">
      <property name="font">
       <font>
        <family>Arial</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>N/A</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="spinner" native="true">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>0</y>
     <width>40</width>
     <height>40</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
  </widget>
  <zorder>spinner</zorder>
  <zorder>buttonBox</zorder>
  <zorder>top_right_logo</zorder>
  <zorder>frame</zorder>
  <zorder>label</zorder>
  <zorder>horizontalLayoutWidget</zorder>
 </widget>
 <resources>
  <include location="../resources.qrc"/>
  <include location="../resources.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>DialogShowQrCode</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
