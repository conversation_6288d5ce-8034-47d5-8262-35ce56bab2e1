<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogConfirmOff</class>
 <widget class="QDialog" name="DialogConfirmOff">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>480</width>
    <height>320</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(255, 128, 128)</string>
  </property>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>9</x>
     <y>9</y>
     <width>16</width>
     <height>16</height>
    </rect>
   </property>
   <property name="maximumSize">
    <size>
     <width>110</width>
     <height>320</height>
    </size>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap>:/RaspiBlitz/images/RaspiBlitz_Logo_Main_rotate.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="indent">
    <number>-4</number>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>47</width>
     <height>318</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="../resources.qrc">:/RaspiBlitz/images/RaspiBlitz_Logo_Main_270.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>102</x>
     <y>30</y>
     <width>320</width>
     <height>64</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>20</pointsize>
     <weight>50</weight>
     <italic>false</italic>
     <bold>false</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>Shutdown RaspiBlitz?</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignHCenter|Qt::AlignTop</set>
   </property>
  </widget>
  <widget class="QDialogButtonBox" name="buttonBox">
   <property name="geometry">
    <rect>
     <x>102</x>
     <y>110</y>
     <width>320</width>
     <height>340</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>0</width>
     <height>0</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>16777215</width>
     <height>16777215</height>
    </size>
   </property>
   <property name="font">
    <font>
     <family>Arial</family>
     <pointsize>28</pointsize>
     <weight>50</weight>
     <italic>false</italic>
     <bold>false</bold>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: lightgrey;
font: 28pt &quot;Arial&quot;;</string>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <property name="standardButtons">
    <set>QDialogButtonBox::Cancel|QDialogButtonBox::Retry|QDialogButtonBox::Yes</set>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="../resources.qrc"/>
  <include location="../resources.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>DialogConfirmOff</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>DialogConfirmOff</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
