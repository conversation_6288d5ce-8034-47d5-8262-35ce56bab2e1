#!/bin/bash

# get basic system information
# these are the same set of infos the WebGUI dialog/controler has
source /home/<USER>/raspiblitz.info


source <(/home/<USER>/_cache.sh get ui_migration_upload ui_migration_uploadUnix ui_migration_uploadWin)
if [ "${ui_migration_upload}" = "1" ]; then

  sudo /home/<USER>/config.scripts/blitz.migration.sh import-gui

  /home/<USER>/_cache.sh set state "waitprovision"
  exit 0
fi

# break loop if no matching if above
/home/<USER>/_cache.sh set state "error"
exit 1