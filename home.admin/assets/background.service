# Monitor the RaspiBlitz
# /etc/systemd/system/background.service

[Unit]
Description=RaspiBlitz Background Monitoring Service
Wants=network.target
After=network.target

# for use with sendmail alert (coming soon)
#OnFailure=systemd-sendmail@%n

[Service]
User=root
Group=root
Type=simple
ExecStart=/home/<USER>/_background.sh
Restart=always
TimeoutSec=10
RestartSec=10
StandardOutput=journal

[Install]
WantedBy=multi-user.target