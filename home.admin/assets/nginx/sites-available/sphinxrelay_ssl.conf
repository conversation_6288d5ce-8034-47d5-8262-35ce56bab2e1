## lnbits_ssl.conf

server {
    listen 3301 ssl http2;
    listen [::]:3301 ssl http2;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data.conf;

    include /etc/nginx/snippets/gzip-params.conf;

    access_log /var/log/nginx/access_sphinxrelay.log;
    error_log /var/log/nginx/error_sphinxrelay.log;

    location /static {
        root /home/<USER>/sphinxrelay/sphinxrelay;
    }

    location / {
        proxy_pass http://127.0.0.1:3300;

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }

}
