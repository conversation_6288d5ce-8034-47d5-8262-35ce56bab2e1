## helipad_tor_ssl.conf

server {
    listen 2115 ssl;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data-tor.conf;

    access_log /var/log/nginx/access_helipad.log;
    error_log /var/log/nginx/error_helipad.log;

    location / {
        proxy_pass http://127.0.0.1:2112;

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }

}
