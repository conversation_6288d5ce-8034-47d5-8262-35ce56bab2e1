## btcpay_tor_ssl.conf

server {
    listen 23003 ssl http2;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data-tor.conf;

    include /etc/nginx/snippets/gzip-params.conf;

    access_log /var/log/nginx/access_btcpay.log;
    error_log /var/log/nginx/error_btcpay.log;

    location / {
        proxy_pass http://127.0.0.1:23000;

	    # For websockets
        proxy_set_header Upgrade $http_upgrade;
	    proxy_set_header Connection $http_connection;

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }

}
