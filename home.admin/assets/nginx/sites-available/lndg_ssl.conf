## lndg_ssl.conf

server {
    listen 8888 ssl http2;
    listen [::]:8888 ssl http2;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data.conf;

    include /etc/nginx/snippets/gzip-params.conf;

    access_log /var/log/nginx/access_lndg.log;
    error_log /var/log/nginx/error_lndg.log;

    location / {
        proxy_pass http://127.0.0.1:8889;

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }

}

