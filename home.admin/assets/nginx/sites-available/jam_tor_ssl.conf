## jam_tor_ssl.conf

server {
    listen 7503 ssl http2;
    listen [::]:7503 ssl http2;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data-tor.conf;

    access_log /var/log/nginx/access_jam.log;
    error_log /var/log/nginx/error_jam.log;

    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_types *;

    root /home/<USER>/webui/build;
    index index.html;

    location /api/ {
        include /etc/nginx/snippets/ssl-proxy-params.conf;

        proxy_set_header Authorization $http_x_jm_authorization;
        proxy_set_header x-jm-authorization "";

        proxy_pass https://127.0.0.1:28183;
    }

    location /jmws {
        include /etc/nginx/snippets/proxy-params.conf;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Authorization "";
        proxy_pass https://127.0.0.1:28283/;
    }

    location /obwatch/ {
        include /etc/nginx/snippets/proxy-params.conf;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        # must proxy via "http" as ob-watcher does not make use of self-signed cert yet
        proxy_pass  http://127.0.0.1:62601/;
    }

    location / {
        include /etc/nginx/snippets/ssl-proxy-params.conf;
        try_files $uri $uri/ /index.html;
        add_header Cache-Control no-cache;
    }
}
