## lnbits_tor_ssl.conf

server {
    listen 5003 ssl http2;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data-tor.conf;

    include /etc/nginx/snippets/gzip-params.conf;

    access_log /var/log/nginx/access_lnbits.log;
    error_log /var/log/nginx/error_lnbits.log;

    location /static {
        root /home/<USER>/lnbits/lnbits;
    }

    location / {
        proxy_pass https://127.0.0.1:5001;

        # needed for websocket connections
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }

}
