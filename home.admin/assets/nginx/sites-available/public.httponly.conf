# RaspiBlitz public.conf server configuration
#
server {
	listen 80 default_server;
	listen [::]:80 default_server;

	root /var/www/public;
	index index.html;
	server_name _;

	include /etc/nginx/snippets/gzip-params.conf;

	# proxy for API
	location /api/ {
		proxy_pass        http://127.0.0.1:11111/;
		proxy_set_header  X-Real-IP $remote_addr;
		proxy_set_header  X-Forwarded-Host   $host;
	}

	# directory for acme challenge
	location ^~ /.well-known/acme-challenge/ {
		default_type "text/plain";
		root /var/www/letsencrypt;
	}

	location = /index.html {
		# internal means that it can't be called from outside
		internal;
		# add no-store to prevent caching of index.html
		add_header Cache-Control 'no-store';
	}

	location / {
		# make sure to have https link to exact same host that was called
		sub_filter '<a href="https://HOST_SET_BY_NGINX/' '<a href="https://$host/';

		# First attempt to serve request as file, then
		# as directory, then fall back to displaying a 404.
		try_files $uri $uri/ /index.html =404;
		add_header Cache-Control "no-cache, must-revalidate";
	}

}

