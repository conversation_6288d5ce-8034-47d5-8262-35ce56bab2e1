## lnproxy_tor_ssl.conf

server {
    listen 4750 ssl http2;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data.conf;

    include /etc/nginx/snippets/gzip-params.conf;

    access_log /var/log/nginx/access_lnproxy.log;
    error_log /var/log/nginx/error_lnproxy.log;

    location / {
        proxy_pass http://127.0.0.1:4747;

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }
}
