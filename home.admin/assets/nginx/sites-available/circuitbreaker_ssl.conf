## circuitbreaker_ssl.conf

server {
    listen 9236 ssl http2;
    listen [::]:9236 ssl http2;
    server_name _;

    include /etc/nginx/snippets/ssl-params.conf;
    include /etc/nginx/snippets/ssl-certificate-app-data.conf;

    include /etc/nginx/snippets/gzip-params.conf;

    access_log /var/log/nginx/access_circuitbreaker.log;
    error_log /var/log/nginx/error_circuitbreaker.log;

    location / {
        proxy_pass http://127.0.0.1:9235;

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }
}
