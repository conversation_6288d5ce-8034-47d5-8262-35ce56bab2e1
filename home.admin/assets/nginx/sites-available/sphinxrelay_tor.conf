## lnbits_tor.conf

server {
    listen 3302;
    server_name _;

    include /etc/nginx/snippets/gzip-params.conf;

    access_log /var/log/nginx/access_sphinxrelay.log;
    error_log /var/log/nginx/error_sphinxrelay.log;

    location /static {
        root /home/<USER>/sphinxrelay/sphinxrelay;
    }

    location / {
        proxy_pass http://127.0.0.1:3300;

        include /etc/nginx/snippets/ssl-proxy-params.conf;
    }

}
