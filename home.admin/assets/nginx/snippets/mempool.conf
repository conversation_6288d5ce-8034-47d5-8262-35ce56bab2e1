	access_log /var/log/nginx/access_mempool.log;
	error_log /var/log/nginx/error_mempool.log;

	root /var/www/mempool/browser;

	index index.html;

	# fallback for all URLs i.e. /address/foo /tx/foo /block/000
	location / {
		try_files /$lang/$uri /$lang/$uri/ $uri $uri/ /en-US/$uri @index-redirect;
	}
	location @index-redirect {
		add_header vary accept-language;
		rewrite (.*) /$lang/index.html;
	}

	# location block using regex are matched in order

	# used to rewrite resources from /<lang>/ to /en-US/
	location ~ ^/(ar|bg|bs|ca|cs|da|de|et|el|es|eo|eu|fa|fr|gl|ko|hr|id|it|he|ka|lv|lt|hu|mk|ms|nl|ja|ka|no|nb|nn|pl|pt|pt-BR|ro|ru|sk|sl|sr|sh|fi|sv|th|tr|uk|vi|zh)/resources/ {
		rewrite ^/[a-zA-Z-]*/resources/(.*) /en-US/resources/$1;
	}
	# used for cookie override
	location ~ ^/(ar|bg|bs|ca|cs|da|de|et|el|es|eo|eu|fa|fr|gl|ko|hr|id|it|he|ka|lv|lt|hu|mk|ms|nl|ja|ka|no|nb|nn|pl|pt|pt-BR|ro|ru|sk|sl|sr|sh|fi|sv|th|tr|uk|vi|zh)/ {
		try_files $uri $uri/ /$1/index.html =404;
	}

	# static API docs
	location = /api {
		try_files $uri $uri/ /en-US/index.html =404;
	}
	location = /api/ {
		try_files $uri $uri/ /en-US/index.html =404;
	}

	# mainnet API
	location /api/v1/donations {
		resolver 1.1.1.1;
		proxy_pass https://mempool.space;
	}
	location /api/v1/donations/images {
		resolver 1.1.1.1;
		proxy_pass https://mempool.space;
	}
	location /api/v1/ws {
		proxy_pass http://127.0.0.1:8999/;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection "Upgrade";
	}
	location /api/v1 {
		proxy_pass http://127.0.0.1:8999/api/v1;
	}
	location /api/ {
		proxy_pass http://127.0.0.1:8999/api/v1/;
	}

	# mainnet API
	location /ws {
		proxy_pass http://127.0.0.1:8999/;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection "Upgrade";
	}
