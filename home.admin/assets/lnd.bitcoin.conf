# lnd configuration - some values might be overruled directly systemd-service exec call parameters

[Application Options]
debuglevel=info
maxpendingchannels=5
alias=raspiblitz
color=#68F442
nat=false

# Avoid historical graph data sync
ignore-historical-gossip-filters=1
# Avoid high startup overhead
stagger-initial-reconnect=1

# Delete and recreate RPC TLS certificate when details change or cert expires
tlsautorefresh=1
# Do not include IPs in the RPC TLS certificate
tlsdisableautofill=1

# RPC open to all connections on Port 10009
rpclisten=0.0.0.0:10009
# REST open to all connections on Port 8080
restlisten=0.0.0.0:8080

[Bitcoin]
bitcoin.active=1
bitcoin.node=bitcoind
# enable either testnet or mainnet
#bitcoin.testnet=1
bitcoin.mainnet=1

[autopilot]
autopilot.active=0
autopilot.maxchannels=5
autopilot.allocation=0.6

[bolt]
db.bolt.auto-compact=true
db.bolt.auto-compact-min-age=672h

# Allow for longer latency, especially useful for <8GB RAM Pi and congested mempool
[healthcheck] 
healthcheck.chainbackend.attempts=3
healthcheck.chainbackend.timeout=2m0s 
healthcheck.chainbackend.interval=1m30s
