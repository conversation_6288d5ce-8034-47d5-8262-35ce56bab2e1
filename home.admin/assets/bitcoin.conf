# bitcoind configuration
# some values might be overruled directly systemd-service exec call parameters

# mainnet/testnet
testnet=0

# Bitcoind options
server=1
daemon=1
txindex=0
disablewallet=1
peerbloomfilters=1
datadir=/mnt/hdd/bitcoin
main.debuglogfile=/mnt/hdd/bitcoin/debug.log
test.debuglogfile=/mnt/hdd/bitcoin/testnet3/debug.log
signet.debuglogfile=/mnt/hdd/bitcoin/signet/debug.log

# Connection settings
rpcuser=raspibolt
rpcpassword=passwordB
main.rpcport=8332
test.rpcport=18332
rpcallowip=127.0.0.1
main.rpcbind=127.0.0.1:8332
test.rpcbind=127.0.0.1:18332
zmqpubrawblock=tcp://127.0.0.1:28332
zmqpubrawtx=tcp://127.0.0.1:28333

# Raspberry Pi optimizations
dbcache=128
maxorphantx=10
maxmempool=300
maxconnections=40
maxuploadtarget=5000

# tor by default
onlynet=onion
proxy=127.0.0.1:9050
main.bind=127.0.0.1
test.bind=127.0.0.1
dnsseed=0
dns=0
