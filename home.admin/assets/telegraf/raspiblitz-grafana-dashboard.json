{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "limit": 100, "name": "Annotations & Alerts", "showIn": 0, "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "matchAny": true, "name": "Show reboots", "showIn": 0, "tags": ["reboot"], "target": {"limit": 100, "matchAny": true, "tags": ["reboot"], "type": "tags"}, "type": "tags"}, {"datasource": {"type": "datasource", "uid": "grafana"}, "enable": false, "hide": true, "iconColor": "#5794F2", "limit": 100, "name": "Global Annotations", "showIn": 0, "tags": ["global"], "type": "tags"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 69, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "Quick Summary - Node", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 1}, "hideTimeOverride": true, "id": 70, "interval": "", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [], "limit": "", "measurement": "system", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"uptime\") FROM \"system\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}], "tz": ""}], "timeFrom": "2m", "title": "system", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 1}, "hideTimeOverride": true, "id": 92, "interval": "", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "/^bitcoind$/", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_service", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["service"], "type": "tag"}], "limit": "", "measurement": "service_uptime", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"uptime\") FROM \"service_uptime\" WHERE (\"host\" =~ /^$host$/ AND \"service\" = 'bitcoind') AND $timeFilter GROUP BY \"service\"", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "service", "operator": "=", "value": "bitcoind"}], "tz": ""}], "timeFrom": "2m", "title": "bitcoind - service", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 1}, "hideTimeOverride": true, "id": 71, "interval": "", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "/^lnd$/", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_service", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["service"], "type": "tag"}], "limit": "", "measurement": "service_uptime", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"uptime\") FROM \"service_uptime\" WHERE (\"host\" =~ /^$host$/ AND \"service\" = 'lnd') AND $timeFilter GROUP BY \"service\"", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "service", "operator": "=", "value": "lnd"}], "tz": ""}], "timeFrom": "2m", "title": "lnd - service", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 1}, "hideTimeOverride": true, "id": 72, "interval": "", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "/^electrs$/", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_service", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["service"], "type": "tag"}], "limit": "", "measurement": "service_uptime", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"uptime\") FROM \"service_uptime\" WHERE (\"host\" =~ /^$host$/ AND \"service\" = 'electrs') AND $timeFilter GROUP BY \"service\"", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "service", "operator": "=", "value": "electrs"}], "tz": ""}], "timeFrom": "2m", "title": "electrs - service", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 3}, {"color": "#299c46", "value": 6}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 15, "y": 1}, "id": 47, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$col", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "bitcoin_nettotals", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"totalbytesrecv\") AS \"Recv\" FROM \"bitcoin_nettotals\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time($__interval) fill(null)", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["totalbytesrecv"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["Recv"], "type": "alias"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Bitcoin - Recv", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 3}, {"color": "#299c46", "value": 6}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 18, "y": 1}, "id": 48, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$col", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "bitcoin_nettotals", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"totalbytessent\") AS \"Sent\" FROM \"bitcoin_nettotals\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time($__interval) fill(null)", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["totalbytessent"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["<PERSON><PERSON>"], "type": "alias"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Bitcoin - Sent", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 21, "y": 1}, "id": 51, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "bitcoin_blockchaininfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"size_on_disk\") FROM \"bitcoin_blockchaininfo\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time($__interval) fill(null)", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["size_on_disk"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Blockchain Size", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 3}, {"color": "#299c46", "value": 6}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 5}, "id": 81, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "bitcoin_connectioncount", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Bitcoin peers", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 3}, {"color": "#299c46", "value": 6}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 5}, "id": 82, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "ln_info", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"num_peers\") FROM \"ln-info\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time($__interval) fill(null)", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["num_peers"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "LN peers", "transparent": true, "type": "stat"}, {"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 97, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "Quick Summary - IP Addresses", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 10}, "hideTimeOverride": true, "id": 95, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"value\") FROM \"bitcoin-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "IPv4"}], "tz": ""}], "timeFrom": "2m", "title": "IPv4 (global)", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 3, "y": 10}, "hideTimeOverride": true, "id": 105, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "name"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"value\") FROM \"bitcoin-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "IPv4"}], "tz": ""}], "timeFrom": "2m", "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 12, "y": 10}, "hideTimeOverride": true, "id": 98, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"value\") FROM \"bitcoin-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "bitcoind"}], "tz": ""}], "timeFrom": "2m", "title": "bitcoind", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 15, "y": 10}, "hideTimeOverride": true, "id": 107, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "name"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"value\") FROM \"bitcoin-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "bitcoind"}], "tz": ""}], "timeFrom": "2m", "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 13}, "hideTimeOverride": true, "id": 100, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"uptime\") FROM \"raspiblitz_ip_info\" WHERE (\"host\" =~ /^$host$/ AND \"origin\" = 'publicIP') AND $timeFilter GROUP BY \"ipaddr\"", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "publicIP"}], "tz": ""}], "timeFrom": "2m", "title": "public IPv4", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 3, "y": 13}, "hideTimeOverride": true, "id": 106, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "name"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"value\") FROM \"bitcoin-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "publicIP"}], "tz": ""}], "timeFrom": "2m", "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 12, "y": 13}, "hideTimeOverride": true, "id": 99, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"value\") FROM \"bitcoin-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "lnd"}], "tz": ""}], "timeFrom": "2m", "title": "lnd", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 7200}, {"color": "#299c46", "value": 36000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 15, "y": 13}, "hideTimeOverride": true, "id": 108, "interval": "1m", "links": [], "maxDataPoints": 1, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "name"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}], "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"value\") FROM \"bitcoin-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "origin", "operator": "=", "value": "lnd"}], "tz": ""}], "timeFrom": "2m", "type": "stat"}, {"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 74, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "Quick Summary - Blocks", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 17}, "hideTimeOverride": true, "id": 75, "interval": "1m", "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [], "limit": "1", "measurement": "web_bitcoin_info", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"blocks\") FROM \"bitcoin-blockchaininfo\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["n_blocks_total"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "10m", "title": "Bitcoin Blocks (Web)", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 17}, "hideTimeOverride": true, "id": 76, "interval": "1m", "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [], "limit": "1", "measurement": "bitcoin_blockchaininfo", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"blocks\") FROM \"bitcoin-blockchaininfo\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["blocks"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "10m", "title": "Bitcoin Blocks", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 17}, "hideTimeOverride": true, "id": 77, "interval": "", "links": [], "maxDataPoints": 10, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [], "limit": "1", "measurement": "ln_info", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"blocks\") FROM \"bitcoin-blockchaininfo\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["block_height"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "10m", "title": "LN BlockHeight", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 17}, "hideTimeOverride": true, "id": 78, "interval": "1m", "links": [], "maxDataPoints": 10, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [], "limit": "1", "measurement": "electrs_info", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"blocks\") FROM \"bitcoin-blockchaininfo\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["index_height"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "10m", "title": "electrs IndexHeight", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "orange", "value": 130}, {"color": "semi-dark-purple", "value": 140}, {"color": "blue", "value": 150}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 21}, "hideTimeOverride": true, "id": 86, "interval": "1m", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"alias": "Block Beat", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "web_bitcoin_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["n_blocks_total"], "type": "field"}, {"params": [], "type": "last"}, {"params": [], "type": "difference"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "24h", "title": "Block Beat / 24h (Web)", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "orange", "value": 130}, {"color": "semi-dark-purple", "value": 140}, {"color": "blue", "value": 150}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 21}, "hideTimeOverride": true, "id": 87, "interval": "1m", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"alias": "Block Beat", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "bitcoin_blockchaininfo", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["blocks"], "type": "field"}, {"params": [], "type": "last"}, {"params": [], "type": "difference"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "24h", "title": "Block Beat / 24h", "transparent": true, "type": "stat"}, {"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 90, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "Quick Summary - MemPool", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10000}, {"color": "#d44a3a", "value": 20000}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 26}, "id": 34, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "limit": "", "measurement": "electrs_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mempool_txs_count"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Pending Tx", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "orange"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 26}, "id": 66, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_fee_rate", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["fee_rate"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "limit": "", "measurement": "electrs_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mempool_txs_vsize"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "fee_rate", "operator": "=", "value": "[                   0,                    1)"}]}], "title": "< 1 sat/vbyte", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "orange"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 26}, "id": 67, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "value"}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_fee_rate", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "limit": "", "measurement": "electrs_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mempool_txs_vsize"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "fee_rate", "operator": "=", "value": "[                   2,                    4)"}, {"condition": "OR", "key": "fee_rate", "operator": "=", "value": "[                   0,                    1)"}]}], "title": "< 2 sat/vbyte", "transparent": true, "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 26}, "hiddenSeries": false, "id": 32, "interval": "1m", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"alias": "$tag_fee_rate", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["fee_rate"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "electrs_info", "orderByTime": "ASC", "policy": "default", "query": "SELECT \"electrs_mempool_vsize\"  / __series.field FROM \"autogen\".\"prometheus\" WHERE $timeFilter GROUP BY \"fee_rate\"", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mempool_txs_vsize"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "mempool_txs_vsize", "operator": ">", "value": "0"}]}], "thresholds": [], "timeFrom": "6h", "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "fees", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": false, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": false}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 30}, "id": 57, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "api.blockchain.info", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-purple"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 31}, "hideTimeOverride": true, "id": 58, "interval": "1m", "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [], "limit": "1", "measurement": "web_bitcoin_info", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"blocks\") FROM \"bitcoin-blockchaininfo\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["n_blocks_total"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "10m", "title": "Bitcoin Blocks (Web)", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "orange", "value": 130}, {"color": "semi-dark-purple", "value": 140}, {"color": "blue", "value": 150}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 31}, "hideTimeOverride": true, "id": 65, "interval": "1m", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"alias": "Block Beat", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "web_bitcoin_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["n_blocks_total"], "type": "field"}, {"params": [], "type": "last"}, {"params": [], "type": "difference"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "24h", "title": "Block Beat / 24h (Web)", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 31}, "id": 61, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"alias": "Blocks mined", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "limit": "", "measurement": "web_bitcoin_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["n_blocks_mined"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Blocks Mined / 24h", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"decimals": 3, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46"}]}, "unit": "currencyBTC"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 31}, "id": 60, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"alias": "Fees per 24h", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "limit": "", "measurement": "web_bitcoin_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["total_fees_btc"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [" / 100000000"], "type": "math"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Fees / 24h", "transparent": true, "type": "stat"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46"}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 31}, "id": 91, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.1.5", "targets": [{"alias": "Marketprice USD", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "limit": "", "measurement": "web_bitcoin_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["market_price_usd"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Marketprice USD", "transparent": true, "type": "stat"}, {"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 25, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "Uptime & Connections", "type": "row"}, {"columns": [], "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fontSize": "100%", "gridPos": {"h": 10, "w": 12, "x": 0, "y": 36}, "id": 22, "interval": "10s", "links": [], "pluginVersion": "6.3.6", "scroll": true, "showHeader": true, "sort": {"desc": false}, "styles": [{"alias": "Last seen", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Uptime", "align": "auto", "colorMode": "row", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "Value", "thresholds": ["36000", "360000"], "type": "number", "unit": "dtdurations"}, {"alias": "service", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Metric", "thresholds": [], "type": "string", "unit": "short"}], "targets": [{"alias": "system", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [], "hide": false, "limit": "1", "measurement": "system", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"uptime\") FROM \"autogen\".\"system\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "$tag_service", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["service"], "type": "tag"}], "limit": "1", "measurement": "service_uptime", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"uptime\") FROM \"autogen\".\"service-uptime\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY \"service\" LIMIT 1", "rawQuery": false, "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "title": "Uptime system / services", "transform": "timeseries_to_rows", "transparent": true, "type": "table-old"}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"custom": {"align": "left", "displayMode": "auto", "inspect": false}, "decimals": 2, "mappings": [], "noValue": "No Data", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "decbytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Last seen"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 36}, "hideTimeOverride": true, "id": 6, "interval": "1m", "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 1, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Value"}]}, "pluginVersion": "9.1.5", "targets": [{"alias": "$col", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}], "limit": "1", "measurement": "bitcoin_nettotals", "orderByTime": "DESC", "policy": "autogen", "query": "SELECT last(\"totalbytesrecv\") AS \"Bytes recv\" FROM \"autogen\".\"bitcoin-nettotals\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY \"host\" LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["totalbytesrecv"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["Bytes recv"], "type": "alias"}], [{"params": ["totalbytessent"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["Bytes sent"], "type": "alias"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "$col", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}], "limit": "1", "measurement": "bitcoin_blockchaininfo", "orderByTime": "DESC", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["size_on_disk"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["Size on disk"], "type": "alias"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "timeFrom": "10m", "title": "Bitcoin - totals", "transformations": [{"id": "seriesToRows", "options": {}}], "transparent": true, "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"unit": "dateTimeAsIso"}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 46}, "hiddenSeries": false, "id": 104, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/ = ::1/", "legend": false}, {"alias": "/^eth0_.+/", "legend": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "$tag_origin = $tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["origin"], "type": "tag"}, {"params": ["ipaddr"], "type": "tag"}], "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["created"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["*1000"], "type": "math"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [], "title": "IP Address History", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "dateTimeAsIso", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"custom": {"align": "center", "displayMode": "color-background", "inspect": false}, "decimals": 1, "mappings": [], "noValue": "No Value transmitted", "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "orange", "value": 3600}, {"color": "green", "value": 72000}]}, "unit": "dtdurations"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Last seen"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 46}, "id": 94, "interval": "1m", "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "uptime"}]}, "pluginVersion": "9.1.5", "targets": [{"alias": "$tag_origin = $tag_ipaddr", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["ipaddr"], "type": "tag"}, {"params": ["origin"], "type": "tag"}], "hide": false, "limit": "", "measurement": "ipinfo", "orderByTime": "ASC", "policy": "autogen", "query": "SELECT last(\"uptime\") FROM \"autogen\".\"system\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter LIMIT 1", "rawQuery": false, "refId": "A", "resultFormat": "table", "select": [[{"params": ["uptime"], "type": "field"}, {"params": [], "type": "last"}, {"params": ["uptime"], "type": "alias"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}], "tz": ""}], "timeFrom": "10m", "title": "IP addresses from services and network devices", "transparent": true, "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 54}, "hiddenSeries": false, "id": 102, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": true, "targets": [{"alias": "$tag_origin", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["origin"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "ipinfo", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["changed"], "type": "field"}, {"params": [], "type": "max"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [], "title": "IP Address changes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "none", "logBase": 1, "max": "5", "min": "-1", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 62}, "hiddenSeries": false, "id": 31, "interval": "10s", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "bootstrap.service", "bars": true, "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "$tag_systemd_unit", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["systemd_unit"], "type": "tag"}], "limit": "", "measurement": "procstat", "orderByTime": "ASC", "policy": "default", "query": "SELECT last(\"created_at\")  / 1000000 FROM \"procstat\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time($__interval), \"systemd_unit\"", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["created_at"], "type": "field"}, {"params": [], "type": "last"}, {"params": [" / 1000000"], "type": "math"}]], "slimit": "", "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "Services Start History", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "dateTimeFromNow", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "decimals": 0, "fill": 0, "fillGradient": 0, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 72}, "hiddenSeries": false, "id": 10, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"alias": "Bitcoin peers", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "bitcoin_connectioncount", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "LN peers", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "ln_info", "orderByTime": "ASC", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["num_peers"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "Peers / Connection Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": "", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 83}, "id": 27, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "Blockchain Info & MemPool", "type": "row"}, {"aliasColors": {"Block Beat": "purple", "Block Beat (24h)": "dark-purple", "Heartbeat": "dark-purple"}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "decimals": 0, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 84}, "hiddenSeries": false, "hideTimeOverride": false, "id": 62, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Block Beat", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "limit": "", "measurement": "bitcoin_blockchaininfo", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["blocks"], "type": "field"}, {"params": [], "type": "last"}, {"params": [], "type": "difference"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [{"colorMode": "custom", "fill": false, "fillColor": "rgba(51, 162, 229, 0.2)", "line": true, "lineColor": "rgba(31, 96, 196, 0.6)", "op": "gt", "value": 6, "yaxis": "left"}], "timeFrom": "24h", "timeRegions": [], "title": "Block Beat", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": false, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": "0", "show": false}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "decimals": 0, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 91}, "hiddenSeries": false, "id": 12, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Bitcoin - Verification Progress", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Bitcoin - Blocks", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "bitcoin_blockchaininfo", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["blocks"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "Bitcoin - Headers", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "bitcoin_blockchaininfo", "orderByTime": "ASC", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["headers"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "Bitcoin - Verification Progress", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "bitcoin_blockchaininfo", "orderByTime": "ASC", "policy": "default", "refId": "E", "resultFormat": "time_series", "select": [[{"params": ["verificationprogress"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "LN - Bockheight", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "ln_info", "orderByTime": "ASC", "policy": "default", "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["block_height"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "electrs - Indexheight", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "electrs_info", "orderByTime": "ASC", "policy": "default", "refId": "D", "resultFormat": "time_series", "select": [[{"params": ["electrs_index_height"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "Blockchain Info", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "none", "label": "", "logBase": 1, "show": true}, {"format": "none", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"Pending Tx": "orange"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 1, "fillGradient": 7, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 101}, "hiddenSeries": false, "id": 41, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"alias": "Pending Tx", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "limit": "", "measurement": "electrs_info", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mempool_txs_count"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "Pending Tx", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 110}, "hiddenSeries": false, "id": 93, "interval": "1m", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:211"}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"alias": "$tag_fee_rate", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["fee_rate"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "electrs_info", "orderByTime": "ASC", "policy": "autogen", "query": "SELECT last(\"mempool_txs_vsize\") FROM \"autogen\".\"electrs_info\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time($__interval), \"fee_rate\" fill(null)", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mempool_txs_vsize"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}, {"condition": "AND", "key": "mempool_txs_vsize", "operator": ">", "value": "0"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "fees via mempool size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 120}, "id": 29, "panels": [], "targets": [{"datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "refId": "A"}], "title": "Computer Metrics", "type": "row"}, {"aliasColors": {"CPU Frequency": "blue", "CPU Temperature": "red"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 121}, "hiddenSeries": false, "id": 18, "interval": "10s", "legend": {"alignAsTable": false, "avg": true, "current": true, "hideEmpty": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "CPU Frequency", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"alias": "CPU Temperature", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "cpu_temperature", "orderByTime": "ASC", "policy": "autogen", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [10], "type": "moving_average"}, {"params": [" / 1000"], "type": "math"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "CPU Frequency", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "cpu_frequency", "orderByTime": "ASC", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["20"], "type": "moving_average"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "CPU Temperature / Frequence", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "celsius", "label": "CPU Temperature", "logBase": 1, "min": "0", "show": true}, {"format": "hertz", "label": "CPU Frequency", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"CPU system": "light-green", "CPU user": "dark-green"}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 129}, "hiddenSeries": false, "id": 20, "interval": "10s", "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"alias": "CPU user", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "cpu", "orderByTime": "ASC", "policy": "default", "query": "SELECT mean(\"usage_user\") AS \"user\", mean(\"usage_system\")AS \"system\" FROM \"autogen\".\"cpu\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time(1m) fill(null)", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["usage_user"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["10"], "type": "moving_average"}]], "tags": [{"key": "cpu", "operator": "=", "value": "cpu-total"}, {"condition": "AND", "key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "CPU system", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host"], "type": "tag"}, {"params": ["cpu"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "cpu", "orderByTime": "ASC", "policy": "autogen", "query": "SELECT mean(\"usage_user\") AS \"user\", mean(\"usage_system\")AS \"system\" FROM \"autogen\".\"cpu\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time(1m) fill(null)", "rawQuery": false, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["usage_system"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [10], "type": "moving_average"}]], "tags": [{"key": "cpu", "operator": "=", "value": "cpu-total"}, {"condition": "AND", "key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "CPU idle", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host"], "type": "tag"}, {"params": ["cpu"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "cpu", "orderByTime": "ASC", "policy": "autogen", "query": "SELECT mean(\"usage_user\") AS \"user\", mean(\"usage_system\")AS \"system\" FROM \"autogen\".\"cpu\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time(1m) fill(null)", "rawQuery": false, "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["usage_idle"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [10], "type": "moving_average"}]], "tags": [{"key": "cpu", "operator": "=", "value": "cpu-total"}, {"condition": "AND", "key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "CPU io wait", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host"], "type": "tag"}, {"params": ["cpu"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "cpu", "orderByTime": "ASC", "policy": "autogen", "query": "SELECT mean(\"usage_user\") AS \"user\", mean(\"usage_system\")AS \"system\" FROM \"autogen\".\"cpu\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time(1m) fill(null)", "rawQuery": false, "refId": "D", "resultFormat": "time_series", "select": [[{"params": ["usage_iowait"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [10], "type": "moving_average"}]], "tags": [{"key": "cpu", "operator": "=", "value": "cpu-total"}, {"condition": "AND", "key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "CPU user/system", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:155", "format": "percent", "label": "", "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:156", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "decimals": 1, "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 137}, "hiddenSeries": false, "id": 16, "interval": "10s", "legend": {"alignAsTable": false, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "avail mem", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"alias": "15min", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "system", "orderByTime": "ASC", "policy": "default", "query": "SELECT mean(\"load1\") AS \"load 1min\", mean(\"load5\") AS \"load 5min\", mean(\"load15\") AS \"load 15min\" FROM \"system\" WHERE $timeFilter GROUP BY time(1m), \"host\"\n", "rawQuery": false, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["load15"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}], "tz": ""}, {"alias": "$col", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "measurement": "mem", "orderByTime": "ASC", "policy": "default", "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["available"], "type": "field"}, {"params": [], "type": "mean"}, {"params": ["avail mem"], "type": "alias"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "SystemLoad / FreeMem", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "bytes", "logBase": 1, "min": "0", "show": true}], "yaxis": {"align": true}}, {"aliasColors": {"Mem avail %": "yellow", "Mem used %": "dark-orange", "Swap used %": "purple"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 145}, "hiddenSeries": false, "id": 44, "interval": "10s", "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/Mem.+/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Swap used %", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "swap", "orderByTime": "ASC", "policy": "default", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["used_percent"], "type": "field"}, {"params": [], "type": "max"}, {"params": [10], "type": "moving_average"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "$col", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "mem", "orderByTime": "ASC", "policy": "default", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["used_percent"], "type": "field"}, {"params": [], "type": "max"}, {"params": [10], "type": "moving_average"}, {"params": ["Mem used %"], "type": "alias"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}, {"alias": "$col", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "mem", "orderByTime": "ASC", "policy": "default", "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["available_percent"], "type": "field"}, {"params": [], "type": "max"}, {"params": [10], "type": "moving_average"}, {"params": ["Mem avail %"], "type": "alias"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "Mem & Swap", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "percent", "logBase": 1, "max": "100", "min": "0", "show": true}], "yaxis": {"align": true}}, {"aliasColors": {"mmcblk0p1": "dark-purple", "mmcblk0p2": "light-purple", "sda1": "super-light-purple"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 153}, "hiddenSeries": false, "id": 23, "interval": "10s", "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "$tag_device", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["device"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "disk", "orderByTime": "ASC", "policy": "autogen", "query": "SELECT mean(\"usage_user\") AS \"user\", mean(\"usage_system\")AS \"system\" FROM \"autogen\".\"cpu\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time(1m) fill(null)", "rawQuery": false, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["used_percent"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [10], "type": "moving_average"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "Disk used %", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "label": "", "logBase": 1, "max": "100", "min": "0", "show": true}, {"decimals": 0, "format": "bytes", "label": "", "logBase": 1, "min": "0", "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"mmcblk0p1": "dark-purple", "mmcblk0p2": "light-purple", "sda1": "super-light-purple"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 161}, "hiddenSeries": false, "id": 42, "interval": "10s", "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "$tag_device", "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["device"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "disk", "orderByTime": "ASC", "policy": "autogen", "query": "SELECT mean(\"usage_user\") AS \"user\", mean(\"usage_system\")AS \"system\" FROM \"autogen\".\"cpu\" WHERE (\"host\" =~ /^$host$/) AND $timeFilter GROUP BY time(1m) fill(null)", "rawQuery": false, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["free"], "type": "field"}, {"params": [], "type": "mean"}, {"params": [10], "type": "moving_average"}]], "tags": [{"key": "host", "operator": "=~", "value": "/^$host$/"}]}], "thresholds": [], "timeRegions": [{"colorMode": "gray", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "fromDayOfWeek": 6, "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time", "toDayOfWeek": 7}], "title": "Disk free space", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "min": "0", "show": true}, {"decimals": 0, "format": "bytes", "label": "", "logBase": 1, "min": "0", "show": false}], "yaxis": {"align": false}}], "refresh": "1m", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "raspberry<PERSON>", "value": "raspberry<PERSON>"}, "datasource": {"type": "influxdb", "uid": "9ocoKgOnz"}, "definition": "SHOW TAG VALUES FROM \"ln_info\" WITH KEY=host", "hide": 0, "includeAll": false, "label": "Host", "multi": false, "name": "host", "options": [], "query": "SHOW TAG VALUES FROM \"ln_info\" WITH KEY=host", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-4d", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Bitcoin/LN/electrs - Node", "uid": "ImRpu1igk", "version": 12, "weekStart": ""}