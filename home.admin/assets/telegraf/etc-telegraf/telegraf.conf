# Telegraf Configuration
#
# raspiblitz


# Global tags can be specified here in key="value" format.
[global_tags]
  ## Environment variables can be used as tags, and throughout the config file
  # user = "$USER"


# Configuration for telegraf agent
[agent]
## Default data collection interval for all inputs
interval                    = "10s"
round_interval              = true
metric_batch_size           = 1000
metric_buffer_limit         = 10000
collection_jitter           = "0s"
flush_interval              = "10s"
flush_jitter                = "0s"
precision                   = ""
hostname                    = ""
omit_hostname               = false



###############################################################################
#                            OUTPUT PLUGINS                                   #
###############################################################################

# Configuration for sending metrics to InfluxDB
[[outputs.influxdb]]
##########
# RaspiBlitz Note:
#   it is necessary to keep the exact spacing for the sed-part in custom-installs.sh
#
urls = ["URL of influxDB with port"]
database = "influxDB database name"
username = "user with write access to above database"
password = "password of the above influx user"
##########
database_tag               = ""
exclude_database_tag       = false
skip_database_creation     = false
retention_policy           = ""
write_consistency          = "any"
timeout                    = "5s"
user_agent                 = "telegraf"
udp_payload                = "512B"

