###############################################################################
#                            INPUT PLUGINS                                    #
###############################################################################

####  Collect statistics about itself
[[inputs.internal]]
  collect_memstats            = true


####  Read metrics about cpu usage
[[inputs.cpu]]
  percpu                      = false
  totalcpu                    = true
  collect_cpu_time            = false
  report_active               = false


####  Read metrics about disk usage by mount point
[[inputs.disk]]
  ignore_fs                   = ["tmpfs", "devtmpfs", "devfs", "iso9660", "overlay", "aufs", "squashfs"]


####  Read metrics about network interface usage
[[inputs.net]]
  ##interfaces                  = ["eth0"]
  ignore_protocol_stats       = false


####  Read metrics about disk IO by device
[[inputs.diskio]]


####  Get kernel statistics from /proc/stat
[[inputs.kernel]]


####  Read metrics about memory usage
[[inputs.mem]]


####  Get the number of processes and group them by status
[[inputs.processes]]


####  Read metrics about swap memory usage
[[inputs.swap]]


####  Read metrics about system load & uptime
[[inputs.system]]


#####################################
####  Bitcoin and Lightning Network related metrics
####
####  Note: [[inputs.exec]] => data_format = "json" only evaluate numeric values.
####        All NON-NUMERIC values will be dropped
####
#####################################


####  Bitcoin related metric
####  basic information about the blockchain
####  --> https://developer.bitcoin.org/reference/rpc/getblockchaininfo.html
##
##    Most useful fields...
##      *   blocks
##      *   headers
##      *   verificationprogress
##
[[inputs.exec]]
  interval                    = "60s"
  commands                    = ["/usr/local/bin/bitcoin-cli  -conf=/mnt/hdd/app-data/bitcoin/bitcoin.conf  getblockchaininfo" ]
  name_override               = "bitcoin_blockchaininfo"
  data_format                 = "json"


####  Bitcoin related metric
####  basic information about the mempool
####  --> https://developer.bitcoin.org/reference/rpc/getmempoolinfo.html
##
##    Most useful fields...
##      *   loaded            (boolean) True if the mempool is fully loaded
##      *   size              (numeric) Current tx count
##      *   usage             (numeric) Total memory usage for the mempool
##
[[inputs.exec]]
  interval                    = "60s"
  commands                    = ["/usr/local/bin/bitcoin-cli  -conf=/mnt/hdd/app-data/bitcoin/bitcoin.conf  getmempoolinfo" ]
  name_override               = "bitcoin_mempoolinfo"
  data_format                 = "json"


####  Bitcoin related metric
####  information about network traffic, including bytes in, bytes out, and current time window
####  --> https://developer.bitcoin.org/reference/rpc/getnettotals.html
##
##    Most useful fields...
##      *   totalbytesrecv
##      *   totalbytessent
##
[[inputs.exec]]
  interval                    = "60s"
  commands                    = ["/usr/local/bin/bitcoin-cli  -conf=/mnt/hdd/app-data/bitcoin/bitcoin.conf  getnettotals" ]
  name_override               = "bitcoin_nettotals"
  data_format                 = "json"


# ####  Bitcoin related metric
# ####  total uptime of the bitcoind service in seconds
# ####  --> https://developer.bitcoin.org/reference/rpc/uptime.html
# ##
# ##    Replaced by "/etc/telegraf/getserviceuptime.sh"
# ##
# [[inputs.exec]]
#   interval                    = "60s"
#   commands                    = ["/usr/local/bin/bitcoin-cli  -conf=/mnt/hdd/app-data/bitcoin/bitcoin.conf  uptime" ]
#   name_override               = "bitcoin_uptime"
#   data_format                 = "value"
#   data_type                   = "integer"


####  Bitcoin related metric
####  number of connections to other nodes
####  --> https://developer.bitcoin.org/reference/rpc/getconnectioncount.html
##
[[inputs.exec]]
  interval                    = "60s"
  commands                    = ["/usr/local/bin/bitcoin-cli  -conf=/mnt/hdd/app-data/bitcoin/bitcoin.conf  getconnectioncount" ]
  name_override               = "bitcoin_connectioncount"
  data_format                 = "value"
  data_type                   = "integer"


####  Lightning Network related metric
####  basic information about the LN node
####  --> https://api.lightning.community/#getinfo
##
##    Most useful fields...
##      *   block_height
##      *   num_peers
##      *   num_active_channels
##      *   num_inactive_channels
##      *   num_pending_channels
##
[[inputs.exec]]
  interval                    = "60s"
  commands                    = ["/usr/local/bin/lncli --lnddir=/mnt/hdd/app-data/lnd getinfo" ]
  name_override               = "ln_info"
  data_format                 = "json"



#####################################
####  Various IP addresses
####
#####################################

#### gets the creation timestamp and uptime of several raspiblitz IP addresses
##
##      *   publicIP from /mnt/hdd/app-data/raspiblitz.conf
##      *   bitcoind node ip address via:   bitcoin-cli getnetworkinfo  =>  "localaddresses"
##      *   lnd ip address via:              lncli getinfo               =>  "uris"
##      *   IPv6 global from eth0/wlan0
##      *   IPv4 local network address from eth0/wlan0
##
[[inputs.exec]]
  interval                    = "60s"
  commands                    = ["/etc/telegraf/getraspiblitzipinfo.sh" ]
  data_format                 = "influx"



#####################################
####  Processes and Services
####
#####################################

#### gets the uptime of various important raspiblitz services via
[[inputs.exec]]
  # this should match the standard metrics-gathering-interval as defined in the [agent] section
  commands                    = ["/etc/telegraf/getserviceuptime.sh" ]
  data_format                 = "influx"


## check for the systemd services not the plain exe-file names
[[inputs.procstat]]
  systemd_unit                = "bootstrap.service"

[[inputs.procstat]]
  systemd_unit                = "telegraf.service"

[[inputs.procstat]]
  systemd_unit                = "ssh.service"

[[inputs.procstat]]
  systemd_unit                = "bitcoind.service"

[[inputs.procstat]]
  systemd_unit                = "lnd.service"

[[inputs.procstat]]
  systemd_unit                = "RTL.service"

[[inputs.procstat]]
  systemd_unit                = "electrs.service"

[[inputs.procstat]]
  systemd_unit                = "btc-rpc-explorer.service"

[[inputs.procstat]]
  systemd_unit                = "background.service"

[[inputs.procstat]]
  systemd_unit                = "mempool.service"



#####################################
####  Hardware data: cpu/gpu temperature, voltage, cpu clock
####
####  2021-04-19  adopt to new path of "vcgencmd" at raspiblitz v1.7.0RC2
#####################################
[[inputs.file]]
  files                       = ["/sys/class/thermal/thermal_zone0/temp"]
  name_override               = "cpu_temperature"
  data_format                 = "value"
  data_type                   = "integer"


[[inputs.exec]]
  commands                    = ["/usr/bin/vcgencmd measure_temp"]
  name_override               = "gpu_temperature"
  data_format                 = "grok"
  grok_patterns               = ["%{NUMBER:value:float}"]


[[inputs.exec]]
  commands                    = ["/usr/bin/vcgencmd measure_volts core"]
  name_override               = "cpu_volts"
  data_format                 = "grok"
  grok_patterns               = ["%{NUMBER:value:float}"]


[[inputs.exec]]
  commands                    = ["/usr/bin/vcgencmd measure_clock arm"]
  name_override               = "cpu_frequency"
  data_format                 = "grok"
  grok_patterns               = ["=%{NUMBER:value:int}"]



#####################################
####  web services
####
#### JSON web interface for statistical data from "blockchain.com"
####
#### documentation:         https://www.blockchain.com/api/charts_api
#### example:               https://www.blockchain.com/stats
#### JSON api URL:          https://api.blockchain.info/stats
####
#####################################
[[inputs.http]]
  interval                    = "60s"
  urls                        = ["https://api.blockchain.info/stats"]
  timeout                     = "5s"
  name_override               = "web_bitcoin_info"
  data_format                 = "json"

###############################################################################
#                            SERVICE INPUT PLUGINS                            #
###############################################################################


####  Read metrics from one or many prometheus clients
#
# electrs stats
[[inputs.prometheus]]
  interval                    = "60s"
  urls                        = ["http://localhost:4224/metrics"]
  metric_version              = 2
  name_override               = "electrs_info"


### -eof-
